{"cells": [{"cell_type": "markdown", "id": "2ebb6782", "metadata": {}, "source": ["Imports & File Loading"]}, {"cell_type": "code", "execution_count": 1, "id": "0c007188", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Files found: ['archive (1)\\\\2017_lake_data.csv', 'archive (1)\\\\2018_lake_data.csv', 'archive (1)\\\\2019_lake_data.csv', 'archive (1)\\\\2020_lake_data.csv', 'archive (1)\\\\2021_lake_data.csv', 'archive (1)\\\\2022_lake_data.csv']\n", "✅ Combined shape: (3195, 20)\n"]}], "source": ["import pandas as pd\n", "import glob\n", "import os\n", "\n", "# Correct relative path from the notebook location\n", "folder_path = \"archive (1)\"\n", "\n", "# List all CSVs in the folder (prints them to confirm)\n", "csv_files = glob.glob(os.path.join(folder_path, \"*.csv\"))\n", "print(\"Files found:\", csv_files)\n", "\n", "# Load\n", "dfs = [pd.read_csv(fp) for fp in csv_files]\n", "df = pd.concat(dfs, ignore_index=True)\n", "\n", "print(f\"✅ Combined shape: {df.shape}\")\n"]}, {"cell_type": "markdown", "id": "19526d00", "metadata": {}, "source": ["Basic Data Inspection"]}, {"cell_type": "code", "execution_count": 2, "id": "bb61c52a", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "STN Code", "rawType": "float64", "type": "float"}, {"name": "Name of Monitoring Location", "rawType": "object", "type": "string"}, {"name": "Type Water Body", "rawType": "object", "type": "string"}, {"name": "State Name", "rawType": "object", "type": "string"}, {"name": "Min Temperature", "rawType": "object", "type": "string"}, {"name": "Max Temperature", "rawType": "object", "type": "unknown"}, {"name": "Min Dissolved Oxygen", "rawType": "object", "type": "string"}, {"name": "Max Dissolved Oxygen", "rawType": "object", "type": "string"}, {"name": "Min pH", "rawType": "object", "type": "unknown"}, {"name": "Max pH", "rawType": "object", "type": "unknown"}, {"name": "Min Conductivity", "rawType": "object", "type": "unknown"}, {"name": "Max Conductivity", "rawType": "object", "type": "unknown"}, {"name": "Min B<PERSON>", "rawType": "object", "type": "unknown"}, {"name": "Max BOD", "rawType": "object", "type": "unknown"}, {"name": "Min Nitrate N + Nitrite N", "rawType": "object", "type": "unknown"}, {"name": "Max Nitrate N + Nitrite N", "rawType": "object", "type": "unknown"}, {"name": "Min Fecal Coliform", "rawType": "object", "type": "unknown"}, {"name": "Max Fe<PERSON>", "rawType": "object", "type": "unknown"}, {"name": "Min Total Coliform", "rawType": "object", "type": "unknown"}, {"name": "Max Total Coliform", "rawType": "object", "type": "unknown"}], "ref": "e28e55ab-1c0e-4457-a7a8-b7da0b226c9f", "rows": [["0", "1790.0", "PULICATE LAKE , NELLORE \nDIST.", "LAKE", "ANDHRA \nPRADESH", "27.0", "28.0", "5.1", "6.9", "7.1", "8.5", "3270.0", "156600.0", "1.0", "2.3", "0.65", "6.9", "2.0", "2.0", "800.0", "1600.0"], ["1", "2353.0", "KONDACHARLA-AAVA LAKE, \nPARAWADA PHARMA CITY, \nVISHAKHAPATNAM, A.P", "LAKE", "ANDHRA \nPRADESH", "24.0", "28.0", "5.9", "6.8", "6.9", "8.4", "597.0", "1034.0", "1.3", "2.3", "1.16", "3.36", "11.0", "29.0", "350.0", "2400.0"], ["2", "2205.0", "MER BEEL AT MADHABPUR, \nASSAM", "LAKE", "ASSAM", "20.0", "27.0", "2.2", "7.2", "5.7", "7.0", "50.0", "128.0", "1.0", "16.2", "0.1", "1.7", "300.0", "2000.0", "360.0", "6400.0"], ["3", "2206.0", "DALONI BEEL NEAR \nJOGIGHOPA, ASSAM", "LAKE", "ASSAM", "22.0", "36.0", "5.1", "6.0", "6.6", "7.8", "54.0", "153.0", "0.9", "2.8", "0.1", "1.4", "300.0", "1200.0", "300.0", "5300.0"], ["4", "1263.0", "ELANGABEEL SYSTEM POND \n(CONNECTED TO R. KOLANG), \nASSAM", "POND", "ASSAM", "22.0", "34.0", "0.7", "4.6", "6.8", "8.4", "263.0", "972.0", "4.5", "14.7", "0.8", "5.7", "1100.0", "3500.0", "730.0", "21000.0"]], "shape": {"columns": 20, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>STN Code</th>\n", "      <th>Name of Monitoring Location</th>\n", "      <th>Type Water Body</th>\n", "      <th>State Name</th>\n", "      <th>Min <PERSON>ature</th>\n", "      <th>Max Temperature</th>\n", "      <th>Min Dissolved Oxygen</th>\n", "      <th><PERSON> Dissolved Oxygen</th>\n", "      <th>Min pH</th>\n", "      <th>Max pH</th>\n", "      <th>Min Conductivity</th>\n", "      <th>Max Conductivity</th>\n", "      <th>Min <PERSON></th>\n", "      <th><PERSON></th>\n", "      <th>Min Nitrate N + Nitrite N</th>\n", "      <th>Max Nitrate N + Nitrite N</th>\n", "      <th>Min Fecal Col<PERSON></th>\n", "      <th>Max <PERSON></th>\n", "      <th>Min Total Coliform</th>\n", "      <th>Max Total Coliform</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1790.0</td>\n", "      <td>PULICATE LAKE , NELLORE \\nDIST.</td>\n", "      <td>LAKE</td>\n", "      <td>ANDHRA \\nPRADESH</td>\n", "      <td>27.0</td>\n", "      <td>28.0</td>\n", "      <td>5.1</td>\n", "      <td>6.9</td>\n", "      <td>7.1</td>\n", "      <td>8.5</td>\n", "      <td>3270.0</td>\n", "      <td>156600.0</td>\n", "      <td>1.0</td>\n", "      <td>2.3</td>\n", "      <td>0.65</td>\n", "      <td>6.9</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>800.0</td>\n", "      <td>1600.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2353.0</td>\n", "      <td>KONDACHARLA-AAVA LAKE, \\nPARAWADA PHARMA CITY,...</td>\n", "      <td>LAKE</td>\n", "      <td>ANDHRA \\nPRADESH</td>\n", "      <td>24.0</td>\n", "      <td>28.0</td>\n", "      <td>5.9</td>\n", "      <td>6.8</td>\n", "      <td>6.9</td>\n", "      <td>8.4</td>\n", "      <td>597.0</td>\n", "      <td>1034.0</td>\n", "      <td>1.3</td>\n", "      <td>2.3</td>\n", "      <td>1.16</td>\n", "      <td>3.36</td>\n", "      <td>11.0</td>\n", "      <td>29.0</td>\n", "      <td>350.0</td>\n", "      <td>2400.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2205.0</td>\n", "      <td>MER BEEL AT MADHABPUR, \\nASSAM</td>\n", "      <td>LAKE</td>\n", "      <td>ASSAM</td>\n", "      <td>20.0</td>\n", "      <td>27.0</td>\n", "      <td>2.2</td>\n", "      <td>7.2</td>\n", "      <td>5.7</td>\n", "      <td>7.0</td>\n", "      <td>50.0</td>\n", "      <td>128.0</td>\n", "      <td>1.0</td>\n", "      <td>16.2</td>\n", "      <td>0.1</td>\n", "      <td>1.7</td>\n", "      <td>300.0</td>\n", "      <td>2000.0</td>\n", "      <td>360.0</td>\n", "      <td>6400.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2206.0</td>\n", "      <td>DALONI BEEL NEAR \\nJOGIGHOPA, ASSAM</td>\n", "      <td>LAKE</td>\n", "      <td>ASSAM</td>\n", "      <td>22.0</td>\n", "      <td>36.0</td>\n", "      <td>5.1</td>\n", "      <td>6.0</td>\n", "      <td>6.6</td>\n", "      <td>7.8</td>\n", "      <td>54.0</td>\n", "      <td>153.0</td>\n", "      <td>0.9</td>\n", "      <td>2.8</td>\n", "      <td>0.1</td>\n", "      <td>1.4</td>\n", "      <td>300.0</td>\n", "      <td>1200.0</td>\n", "      <td>300.0</td>\n", "      <td>5300.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1263.0</td>\n", "      <td>ELANGABEEL SYSTEM POND \\n(CONNECTED TO R. KOLA...</td>\n", "      <td>POND</td>\n", "      <td>ASSAM</td>\n", "      <td>22.0</td>\n", "      <td>34.0</td>\n", "      <td>0.7</td>\n", "      <td>4.6</td>\n", "      <td>6.8</td>\n", "      <td>8.4</td>\n", "      <td>263.0</td>\n", "      <td>972.0</td>\n", "      <td>4.5</td>\n", "      <td>14.7</td>\n", "      <td>0.8</td>\n", "      <td>5.7</td>\n", "      <td>1100.0</td>\n", "      <td>3500.0</td>\n", "      <td>730.0</td>\n", "      <td>21000.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   STN Code                        Name of Monitoring Location  \\\n", "0    1790.0                    PULICATE LAKE , NELLORE \\nDIST.   \n", "1    2353.0  KONDACHARLA-AAVA LAKE, \\nPARAWADA PHARMA CITY,...   \n", "2    2205.0                     MER BEEL AT MADHABPUR, \\nASSAM   \n", "3    2206.0                DALONI BEEL NEAR \\nJOGIGHOPA, ASSAM   \n", "4    1263.0  ELANGABEEL SYSTEM POND \\n(CONNECTED TO R. K<PERSON>A...   \n", "\n", "  Type Water Body        State Name Min Temperature Max Temperature  \\\n", "0            LAKE  ANDHRA \\nPRADESH            27.0            28.0   \n", "1            LAKE  ANDHRA \\nPRADESH            24.0            28.0   \n", "2            LAKE             ASSAM            20.0            27.0   \n", "3            LAKE             ASSAM            22.0            36.0   \n", "4            POND             ASSAM            22.0            34.0   \n", "\n", "  Min Dissolved Oxygen Max Dissolved Oxygen Min pH Max pH Min Conductivity  \\\n", "0                  5.1                  6.9    7.1    8.5           3270.0   \n", "1                  5.9                  6.8    6.9    8.4            597.0   \n", "2                  2.2                  7.2    5.7    7.0             50.0   \n", "3                  5.1                  6.0    6.6    7.8             54.0   \n", "4                  0.7                  4.6    6.8    8.4            263.0   \n", "\n", "  Max Conductivity Min BOD Max BOD Min Nitrate N + Nitrite N  \\\n", "0         156600.0     1.0     2.3                      0.65   \n", "1           1034.0     1.3     2.3                      1.16   \n", "2            128.0     1.0    16.2                       0.1   \n", "3            153.0     0.9     2.8                       0.1   \n", "4            972.0     4.5    14.7                       0.8   \n", "\n", "  Max Nitrate N + Nitrite N Min Fecal Coliform Max Fecal Coliform  \\\n", "0                       6.9                2.0                2.0   \n", "1                      3.36               11.0               29.0   \n", "2                       1.7              300.0             2000.0   \n", "3                       1.4              300.0             1200.0   \n", "4                       5.7             1100.0             3500.0   \n", "\n", "  Min Total Coliform Max Total Coliform  \n", "0              800.0             1600.0  \n", "1              350.0             2400.0  \n", "2              360.0             6400.0  \n", "3              300.0             5300.0  \n", "4              730.0            21000.0  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Show first few rows\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": 3, "id": "95ac6193", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3195 entries, 0 to 3194\n", "Data columns (total 20 columns):\n", " #   Column                       Non-Null Count  Dtype  \n", "---  ------                       --------------  -----  \n", " 0   STN Code                     3155 non-null   float64\n", " 1   Name of Monitoring Location  3167 non-null   object \n", " 2   Type Water Body              3156 non-null   object \n", " 3   State Name                   3156 non-null   object \n", " 4   Min Temperature              3163 non-null   object \n", " 5   Max Temperature              3162 non-null   object \n", " 6   Min Dissolved Oxygen         3176 non-null   object \n", " 7   Max Dissolved Oxygen         3176 non-null   object \n", " 8   Min pH                       3180 non-null   object \n", " 9   Max pH                       3180 non-null   object \n", " 10  Min Conductivity             3162 non-null   object \n", " 11  Max Conductivity             3162 non-null   object \n", " 12  Min BOD                      3171 non-null   object \n", " 13  Max BOD                      3171 non-null   object \n", " 14  Min Nitrate N + Nitrite N    3090 non-null   object \n", " 15  Max Nitrate N + Nitrite N    3090 non-null   object \n", " 16  Min Fecal Coliform           3019 non-null   object \n", " 17  Max Fecal Coliform           3019 non-null   object \n", " 18  Min Total Coliform           3056 non-null   object \n", " 19  Max Total Coliform           3056 non-null   object \n", "dtypes: float64(1), object(19)\n", "memory usage: 499.3+ KB\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "STN Code", "rawType": "float64", "type": "float"}], "ref": "3161a944-7ddf-45f5-894c-06571a40876c", "rows": [["count", "3155.0"], ["mean", "3271.414263074485"], ["std", "1035.3836430919812"], ["min", "1215.0"], ["25%", "2376.0"], ["50%", "3559.0"], ["75%", "4041.0"], ["max", "5291.0"]], "shape": {"columns": 1, "rows": 8}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>STN Code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>3155.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>3271.414263</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1035.383643</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1215.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2376.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3559.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>4041.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>5291.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          STN Code\n", "count  3155.000000\n", "mean   3271.414263\n", "std    1035.383643\n", "min    1215.000000\n", "25%    2376.000000\n", "50%    3559.000000\n", "75%    4041.000000\n", "max    5291.000000"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Data types and nulls\n", "df.info()\n", "\n", "# Missing values count\n", "df.isnull().sum()\n", "\n", "# Quick summary stats\n", "df.describe()\n"]}, {"cell_type": "markdown", "id": "8d29ca97", "metadata": {}, "source": ["Understand Label"]}, {"cell_type": "code", "execution_count": 4, "id": "8e6b5f0f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Available columns: ['STN Code', 'Name of Monitoring Location', 'Type Water Body', 'State Name', 'Min Temperature', 'Max Temperature', 'Min Dissolved Oxygen', 'Max Dissolved Oxygen', 'Min pH', 'Max pH', 'Min Conductivity', 'Max Conductivity', 'Min BOD', 'Max BOD', 'Min Nitrate N + Nitrite N', 'Max Nitrate N + Nitrite N', 'Min Fecal Coliform', 'Max Fecal Coliform', 'Min Total Coliform', 'Max Total Coliform']\n"]}], "source": ["print(\"Available columns:\", df.columns.tolist())\n"]}, {"cell_type": "markdown", "id": "ec4b322e", "metadata": {}, "source": ["Preprocess the Data\n", "Convert Columns to Numeric"]}, {"cell_type": "code", "execution_count": 5, "id": "f1db2f9a", "metadata": {}, "outputs": [], "source": ["# Columns to convert\n", "numeric_cols = [col for col in df.columns if col.startswith(('<PERSON>', '<PERSON>'))]\n", "\n", "# Convert to numeric\n", "for col in numeric_cols:\n", "    df[col] = pd.to_numeric(df[col], errors='coerce')\n"]}, {"cell_type": "code", "execution_count": 6, "id": "7d3c2b0e", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "int64", "type": "integer"}], "ref": "cfacde8c-1453-40bd-acae-f9b249643ab9", "rows": [["Min Temperature", "71"], ["Max Temperature", "71"], ["Min Dissolved Oxygen", "69"], ["Max Dissolved Oxygen", "59"], ["Min pH", "44"], ["Max pH", "43"], ["Min Conductivity", "76"], ["Max Conductivity", "76"], ["Min B<PERSON>", "72"], ["Max BOD", "62"], ["Min Nitrate N + Nitrite N", "305"], ["Max Nitrate N + Nitrite N", "287"], ["Min Fecal Coliform", "337"], ["Max Fe<PERSON>", "329"], ["Min Total Coliform", "371"], ["Max Total Coliform", "305"]], "shape": {"columns": 1, "rows": 16}}, "text/plain": ["Min Temperature               71\n", "Max Temperature               71\n", "Min Dissolved Oxygen          69\n", "Max Dissolved Oxygen          59\n", "Min pH                        44\n", "Max pH                        43\n", "Min Conductivity              76\n", "Max Conductivity              76\n", "<PERSON>                       72\n", "Max BOD                       62\n", "Min Nitrate N + Nitrite N    305\n", "Max Nitrate N + Nitrite N    287\n", "Min Fecal Coliform           337\n", "Max Fecal Coliform           329\n", "Min Total Coliform           371\n", "Max Total Coliform           305\n", "dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Check missing values now\n", "df[numeric_cols].isnull().sum()\n"]}, {"cell_type": "code", "execution_count": 7, "id": "42e3a6b1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ After essential filtering: (2850, 20)\n"]}], "source": ["required_cols = [\n", "    'Min pH', 'Max pH',\n", "    'Min BOD', 'Max BOD',\n", "    'Min Dissolved Oxygen', 'Max Dissolved Oxygen',\n", "    'Max Total Coliform'  # Used in potability rule\n", "]\n", "\n", "df_clean = df.dropna(subset=required_cols).reset_index(drop=True)\n", "print(\"✅ After essential filtering:\", df_clean.shape)\n"]}, {"cell_type": "markdown", "id": "cdad019b", "metadata": {}, "source": ["Create the Potable label"]}, {"cell_type": "code", "execution_count": 8, "id": "9acbf067", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Relaxed Potability2 counts:\n", " Potable2\n", "0    2259\n", "1     591\n", "Name: count, dtype: int64\n", "\n", "Proportions:\n", " Potable2\n", "0    0.792632\n", "1    0.207368\n", "Name: proportion, dtype: float64\n"]}], "source": ["def is_potable_relaxed(row):\n", "    return int(\n", "        6.5 <= row['Min pH'] <= 8.5 and\n", "        row['Max BOD'] <= 6 and\n", "        row['Min Dissolved Oxygen'] >= 4 and\n", "        row['Max Total Coliform'] <= 500\n", "    )\n", "\n", "# Apply relaxed rule\n", "df_clean['Potable2'] = df_clean.apply(is_potable_relaxed, axis=1)\n", "\n", "# Check new balance\n", "counts = df_clean['Potable2'].value_counts()\n", "props  = df_clean['Potable2'].value_counts(normalize=True)\n", "print(\"Relaxed Potability2 counts:\\n\", counts)\n", "print(\"\\nProportions:\\n\", props)\n"]}, {"cell_type": "markdown", "id": "b536351c", "metadata": {}, "source": [" these are “relaxed potability thresholds for research purposes”, and acknowledge that some “Potable2” samples may not meet strict drinking‑water standards."]}, {"cell_type": "markdown", "id": "35d67105", "metadata": {}, "source": ["Imports & NGBoost Patch"]}, {"cell_type": "code", "execution_count": 1, "id": "9f0b94ad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ NGBoost patched\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import glob, os\n", "\n", "# Patch NGBoost for sklearn compatibility\n", "from sklearn.utils.validation import check_X_y as skl_check_X_y, check_array as skl_check_array\n", "import ngboost.ngboost as _ngb_mod\n", "\n", "def _patched_check_X_y(X, y, **kwargs):\n", "    kwargs.pop(\"ensure_all_finite\", None)\n", "    return skl_check_X_y(X, y, **kwargs)\n", "\n", "def _patched_check_array(X, **kwargs):\n", "    kwargs.pop(\"ensure_all_finite\", None)\n", "    return skl_check_array(X, **kwargs)\n", "\n", "_ngb_mod.check_X_y   = _patched_check_X_y\n", "_ngb_mod.check_array = _patched_check_array\n", "\n", "print(\"✅ NGBoost patched\")\n"]}, {"cell_type": "markdown", "id": "5df37a72", "metadata": {}, "source": ["Load & Concatenate CSVs"]}, {"cell_type": "code", "execution_count": 3, "id": "30f94917", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Files found: ['archive (1)\\\\2017_lake_data.csv', 'archive (1)\\\\2018_lake_data.csv', 'archive (1)\\\\2019_lake_data.csv', 'archive (1)\\\\2020_lake_data.csv', 'archive (1)\\\\2021_lake_data.csv', 'archive (1)\\\\2022_lake_data.csv']\n", "Combined shape: (3195, 20)\n"]}], "source": ["file_paths = glob.glob(\"archive (1)/*.csv\")\n", "print(\"Files found:\", file_paths)\n", "\n", "dfs = [pd.read_csv(fp) for fp in file_paths]\n", "df = pd.concat(dfs, ignore_index=True)\n", "print(\"Combined shape:\", df.shape)\n"]}, {"cell_type": "markdown", "id": "1b1d2302", "metadata": {}, "source": ["Clean Column Names & Convert to Numeric\n", "python\n", "Copy code\n"]}, {"cell_type": "code", "execution_count": 4, "id": "01fe92c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Numeric dtypes: [dtype('float64')]\n"]}], "source": ["# Standardize column names\n", "df.columns = (\n", "    df.columns\n", "      .str.strip()\n", "      .str.lower()\n", "      .str.replace(\" \", \"_\")\n", "      .str.replace(r\"[^0-9a-z_]\", \"\", regex=True)\n", ")\n", "\n", "# Convert all min_/max_ cols to float\n", "num_cols = [c for c in df.columns if c.startswith((\"min_\", \"max_\"))]\n", "df[num_cols] = df[num_cols].apply(pd.to_numeric, errors=\"coerce\")\n", "\n", "print(\"Numeric dtypes:\", df[num_cols].dtypes.unique())\n"]}, {"cell_type": "markdown", "id": "3cef375c", "metadata": {}, "source": ["Filter Essential Columns & Clean"]}, {"cell_type": "code", "execution_count": 5, "id": "a015081f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["After filter: (2862, 20)\n"]}], "source": ["required = [\"min_ph\", \"max_ph\", \"min_dissolved_oxygen\", \"max_bod\", \"max_total_coliform\"]\n", "df_clean = df.dropna(subset=required).reset_index(drop=True)\n", "print(\"After filter:\", df_clean.shape)\n"]}, {"cell_type": "markdown", "id": "5cacd1ab", "metadata": {}, "source": ["Imputation"]}, {"cell_type": "code", "execution_count": 6, "id": "b7c988ce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Remaining NaNs after imputation: 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35216\\2208393327.py:6: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_clean[c].fillna(median, inplace=True)\n"]}], "source": ["num_cols = [c for c in df_clean.columns if c.startswith((\"min_\", \"max_\"))]\n", "\n", "# Median-impute each\n", "for c in num_cols:\n", "    median = df_clean[c].median()\n", "    df_clean[c].fillna(median, inplace=True)\n", "\n", "# Quick check: should be zero\n", "print(\"Remaining NaNs after imputation:\", df_clean[num_cols].isnull().sum().sum())\n"]}, {"cell_type": "markdown", "id": "3c2878cb", "metadata": {}, "source": ["Check Feature Variance"]}, {"cell_type": "code", "execution_count": 7, "id": "9580d66e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature uniqueness:\n", "{'min_temperature': 102, 'max_temperature': 100, 'min_dissolved_oxygen': 94, 'max_dissolved_oxygen': 170, 'min_ph': 52, 'max_ph': 41, 'min_conductivity': 1123, 'max_conductivity': 1606, 'min_bod': 132, 'max_bod': 313, 'min_nitrate_n__nitrite_n': 371, 'max_nitrate_n__nitrite_n': 825, 'min_fecal_coliform': 214, 'max_fecal_coliform': 360, 'min_total_coliform': 295, 'max_total_coliform': 374}\n"]}], "source": ["# Cell X: Diagnose feature variability\n", "print(\"Feature uniqueness:\")\n", "print({c: df_clean[c].nunique() for c in num_cols})\n"]}, {"cell_type": "markdown", "id": "6c532a53", "metadata": {}, "source": ["Create Relaxed Potability Label"]}, {"cell_type": "code", "execution_count": 8, "id": "bc8ea437", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["potable2\n", "0    0.793152\n", "1    0.206848\n", "Name: proportion, dtype: float64\n"]}], "source": ["def is_potable_relaxed(r):\n", "    return int(\n", "        6.5 <= r[\"min_ph\"] <= 8.5 and\n", "        r[\"max_bod\"] <= 6 and\n", "        r[\"min_dissolved_oxygen\"] >= 4 and\n", "        r[\"max_total_coliform\"] <= 500\n", "    )\n", "\n", "df_clean[\"potable2\"] = df_clean.apply(is_potable_relaxed, axis=1)\n", "print(df_clean[\"potable2\"].value_counts(normalize=True))\n"]}, {"cell_type": "markdown", "id": "ee15d263", "metadata": {}, "source": ["Train/Test Split"]}, {"cell_type": "code", "execution_count": 9, "id": "f96f92ce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train pos/neg: 473 1816\n", " Test pos/neg: 119 454\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "X = df_clean[num_cols]\n", "y = df_clean[\"potable2\"]\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, stratify=y, test_size=0.2, random_state=42\n", ")\n", "\n", "print(\"Train pos/neg:\", y_train.sum(), len(y_train)-y_train.sum())\n", "print(\" Test pos/neg:\", y_test.sum(),  len(y_test)-y_test.sum())\n"]}, {"cell_type": "code", "execution_count": 10, "id": "1bbc4d67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train positives: 473 Train negatives: 1816\n", "Test  positives: 119 Test  negatives: 454\n"]}], "source": ["print(\"Train positives:\", y_train.sum(), \"Train negatives:\", len(y_train) - y_train.sum())\n", "print(\"Test  positives:\", y_test.sum(), \"Test  negatives:\", len(y_test) - y_test.sum())\n"]}, {"cell_type": "markdown", "id": "478bd7e9", "metadata": {}, "source": ["Baseline with LightGBM"]}, {"cell_type": "code", "execution_count": 11, "id": "b8daa9d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[LightGBM] [Info] Number of positive: 473, number of negative: 1816\n", "[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.000488 seconds.\n", "You can set `force_col_wise=true` to remove the overhead.\n", "[LightGBM] [Info] Total Bins 2532\n", "[LightGBM] [Info] Number of data points in the train set: 2289, number of used features: 16\n", "[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.206640 -> initscore=-1.345296\n", "[LightGBM] [Info] Start training from score -1.345296\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "[LightGBM] [Warning] No further splits with positive gain, best gain: -inf\n", "📊 LightGBM\n", "Acc : 1.0\n", "F1  : 1.0\n", "AUC : 1.0\n", "Br. : 1.7535243746729224e-10\n"]}], "source": ["import lightgbm as lgb\n", "from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, brier_score_loss\n", "\n", "lgb_train = lgb.Dataset(X_train, label=y_train)\n", "lgb_model = lgb.train(\n", "    {\"objective\": \"binary\", \"metric\": \"binary_logloss\"},\n", "    lgb_train, num_boost_round=200\n", ")\n", "\n", "y_proba_lgb = lgb_model.predict(X_test)\n", "y_pred_lgb  = (y_proba_lgb >= 0.5).astype(int)\n", "\n", "print(\"📊 LightGBM\")\n", "print(\"Acc :\", accuracy_score(y_test, y_pred_lgb))\n", "print(\"F1  :\", f1_score(y_test, y_pred_lgb))\n", "print(\"AUC :\", roc_auc_score(y_test, y_proba_lgb))\n", "print(\"Br. :\", brier_score_loss(y_test, y_proba_lgb))\n"]}, {"cell_type": "markdown", "id": "35a46343", "metadata": {}, "source": ["NGBoost Probabilistic Classifier"]}, {"cell_type": "code", "execution_count": 12, "id": "d7bdc7f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 NGBoost\n", "Acc : 0.0\n", "F1  : 0.0\n", "AUC : 0.0\n", "Br. : 0.9856581472667132\n"]}], "source": ["from ngboost import NGBClassifier\n", "from ngboost.distns import <PERSON><PERSON><PERSON>\n", "from ngboost.scores import LogScore\n", "\n", "ngb = NGBClassifier(\n", "    Dist=<PERSON><PERSON><PERSON>, Score=LogScore,\n", "    n_estimators=500, learning_rate=0.03,\n", "    natural_gradient=True, random_state=42, verbose=False\n", ")\n", "ngb.fit(X_train, y_train)\n", "\n", "p_safe_ngb   = ngb.predict_proba(X_test)[:, 1]\n", "p_unsafe_ngb = 1 - p_safe_ngb\n", "y_pred_ngb   = (p_unsafe_ngb >= 0.5).astype(int)\n", "\n", "print(\"🚀 NGBoost\")\n", "print(\"Acc :\", accuracy_score(y_test, y_pred_ngb))\n", "print(\"F1  :\", f1_score(y_test, y_pred_ngb))\n", "print(\"AUC :\", roc_auc_score(y_test, p_unsafe_ngb))\n", "print(\"Br. :\", brier_score_loss(y_test, p_unsafe_ngb))\n"]}, {"cell_type": "markdown", "id": "b6659c8f", "metadata": {}, "source": ["Custom QRF via RandomForestRegressor"]}, {"cell_type": "code", "execution_count": 13, "id": "0802b871", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌲 Custom QRF\n", "Acc : 0.0\n", "F1  : 0.0\n", "AUC : 0.0\n", "Br. : 1.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\base.py:432: UserWarning: X has feature names, but DecisionTreeRegressor was fitted without feature names\n", "  warnings.warn(\n"]}], "source": ["from sklearn.ensemble import RandomForestRegressor\n", "\n", "rf_reg = RandomForestRegressor(n_estimators=200, max_depth=8, random_state=42)\n", "rf_reg.fit(X_train, 1 - y_train)  \n", "\n", "all_preds   = np.vstack([t.predict(X_test) for t in rf_reg.estimators_]).T\n", "median_pred = np.quantile(all_preds, 0.5, axis=1)\n", "\n", "y_pred_qrf   = (median_pred >= 0.5).astype(int)\n", "p_unsafe_qrf = median_pred\n", "\n", "print(\"🌲 Custom QRF\")\n", "print(\"Acc :\", accuracy_score(y_test, y_pred_qrf))\n", "print(\"F1  :\", f1_score(y_test, y_pred_qrf))\n", "print(\"AUC :\", roc_auc_score(y_test, p_unsafe_qrf))\n", "print(\"Br. :\", brier_score_loss(y_test, p_unsafe_qrf))\n"]}, {"cell_type": "markdown", "id": "c4c34dd2", "metadata": {}, "source": ["Reliability & Uncertainty Plots"]}, {"cell_type": "code", "execution_count": 14, "id": "47a4861f", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjcAAAHHCAYAAABDUnkqAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAACg60lEQVR4nOzdd1gU1/v38ffs0quiCBas2HvvHcVGooliiz1q7NFYY9dYMWqMWGKNHTSxd42999h7F0RFinR29/ljn/D7EjUCAkO5X9e1l7OzUz67Knsz58w5isFgMCCEEEIIkUFo1A4ghBBCCJGcpLgRQgghRIYixY0QQgghMhQpboQQQgiRoUhxI4QQQogMRYobIYQQQmQoUtwIIYQQIkOR4kYIIYQQGYoUN0IIIYTIUKS4EUKo7vDhwyiKwuHDh+PWde3alfz58yfpeIqi0L9//09ut3LlShRF4dGjR3Hr6tWrR7169eKeP3r0CEVRWLlyZZKyCCFSnxQ3QohE+6co+OdhYmJC7ty56dq1K8+fP1c7XorbtWsXEyZMUDuGEOIjTNQOIIRIvyZNmkSBAgWIjIzk9OnTrFy5kuPHj3Pt2jUsLCw+69hLlixBr9cnU9IP69SpE+3atcPc3Pyj2+TLl4+IiAhMTU3j1u3atQtvb28pcIRIo6S4EUIkWdOmTalUqRIA3377LdmzZ2fGjBls27YNT0/Pzzr2/xYTKUWr1aLVav9zG0VRPrtQE0KkLmmWEkIkm9q1awNw//79uHW3bt2idevWODg4YGFhQaVKldi2bdsnj/WhPjezZs2iRo0aZMuWDUtLSypWrMimTZs+eoy1a9dStGhRLCwsqFixIkePHo33+of63Pzbv/vcdO3aFW9vb4B4TXMGg4H8+fPz5ZdfvneMyMhI7O3t6d279yfftxDi88mVGyFEsvmnSMiaNSsA169fp2bNmuTOnZuRI0dibW2Nr68vLVu25I8//qBVq1aJOv4vv/zCF198QceOHYmOjmbDhg20adOGHTt20Lx583jbHjlyBB8fHwYOHIi5uTkLFiygSZMmnD17llKlSiX5Pfbu3ZsXL16wf/9+Vq9eHbdeURS++eYbZs6cSWBgIA4ODnGvbd++nZCQEL755pskn1cIkQgGIYRIpBUrVhgAw4EDBwyvXr0yPH361LBp0yaDo6Ojwdzc3PD06VODwWAwNGzY0FC6dGlDZGRk3L56vd5Qo0YNQ+HChePWHTp0yAAYDh06FLeuS5cuhnz58sU7b3h4eLzn0dHRhlKlShkaNGgQbz1gAAznz5+PW/f48WODhYWFoVWrVu+9j4cPH8atq1u3rqFu3bpxzx8+fGgADCtWrIhb169fP8OHfnzevn3bABgWLlwYb/0XX3xhyJ8/v0Gv17+3jxAi+UmzlBAiydzc3HB0dMTFxYXWrVtjbW3Ntm3byJMnD4GBgfz11194enoSGhrK69evef36NW/evMHd3Z27d+8m+s4qS0vLuOW3b98SHBxM7dq1uXjx4nvbVq9enYoVK8Y9z5s3L19++SV79+5Fp9Ml/U3/hyJFilC1alXWrl0bty4wMJDdu3fTsWNHFEVJkfMKIeKTZikhRJJ5e3tTpEgRgoODWb58OUePHo278+jevXsYDAbGjh3L2LFjP7h/QEAAuXPnTvD5duzYwU8//cTly5eJioqKW/+hoqFw4cLvrStSpAjh4eG8evUKZ2fnBJ83MTp37kz//v15/Pgx+fLlY+PGjcTExNCpU6cUOZ8Q4n1S3AghkqxKlSpxd0u1bNmSWrVq0aFDB27fvh13G/fQoUNxd3f/4P6urq4JPtexY8f44osvqFOnDgsWLCBnzpyYmpqyYsUK1q1b9/lvJpm0a9eOwYMHs3btWn788UfWrFlDpUqVKFq0qNrRhMg0pLgRQiQLrVbLtGnTqF+/PvPnz6d79+6A8ZZuNze3zz7+H3/8gYWFBXv37o03Ls2KFSs+uP3du3ffW3fnzh2srKxwdHT8rCz/1bzk4OBA8+bNWbt2LR07duTEiRPMnTv3s84nhEgc6XMjhEg29erVo0qVKsydOxc7Ozvq1avH4sWL8fPze2/bV69eJerYWq0WRVHi9Zd59OgRW7Zs+eD2p06ditcX5+nTp2zdupXGjRt/cmybT7G2tgYgKCjog6936tSJGzduMGzYMLRaLe3atfus8wkhEkeu3AghktWwYcNo06YNK1euxNvbm1q1alG6dGl69uxJwYIFefnyJadOneLZs2dcuXIlwcdt3rw5s2fPpkmTJnTo0IGAgAC8vb1xdXXl77//fm/7UqVK4e7uHu9WcICJEyd+9nv8p6PywIEDcXd3f6+Aad68OdmyZWPjxo00bdqUHDlyfPY5hRAJJ1duhBDJ6quvvqJQoULMmjWLokWLcv78eZo3b87KlSvp168fixYtQqPRMG7cuEQdt0GDBixbtgx/f3++//571q9fz4wZMz46Vk7dunWZO3cuq1evZty4cTg4OLB7927KlCmTLO9xwIAB7Nmzh06dOtG+fft4r5uZmdG2bVsA6UgshAoUg8FgUDuEEEJkNIMHD44rxqysrNSOI0SmIlduhBAimUVGRrJmzRq+/vprKWyEUIH0uRFCiGQSEBDAgQMH2LRpE2/evGHQoEFqRxIiU5LiRgghksmNGzfo2LEjOXLkYN68eZQrV07tSEJkStLnRgghhBAZivS5EUIIIUSGIsWNEEIIITKUTNfnRq/X8+LFC2xtbWWGXiGEECKdMBgMhIaGkitXLjSa/742k+mKmxcvXuDi4qJ2DCGEEEIkwdOnT8mTJ89/bpPpihtbW1vA+OHY2dmpnEYIIYQQCRESEoKLi0vc9/h/yXTFzT9NUXZ2dlLcCCGEEOlMQrqUSIdiIYQQQmQoUtwIIYQQIkOR4kYIIYQQGYoUN0IIIYTIUKS4EUIIIUSGIsWNEEIIITIUKW6EEEIIkaFIcSOEEEKIDEWKGyGEEEJkKJluhOKUotPruBhwkVfhr3C0cKBCZBTasFdg4wT5aoBGq3bE9Emvg8cn4d3L+J/lx9YLIYRQjU5v4OzDQAJCI8lha0GVAg5oNak/SbWqxc3Ro0fx8vLiwoUL+Pn5sXnzZlq2bPmf+xw+fJghQ4Zw/fp1XFxcGDNmDF27dk2VvB9z4PEBpp+dzsvwl3HrnGJjGfnmLW7hEWCXC5rMgBJfqJgyHbqxDfaMgJAX/7fOLheUag3XNr2/Xj5jIYRQzZ5rfkzcfgO/4Mi4dTntLRjvUYImpXKmahZVm6XCwsIoW7Ys3t7eCdr+4cOHNG/enPr163P58mW+//57vv32W/bu3ZvCST/uwOMDDDk8JF5hAxCg1TIkR3YOWFlCiB/4djZ+WYuEubHN+Jn9bwEDxucn531gvXzGQgihlj3X/Oiz5mK8wgbAPziSPmsusueaX6rmUQwGgyFVz/gRiqJ88srNiBEj2LlzJ9euXYtb165dO4KCgtizZ0+CzhMSEoK9vT3BwcGfPXGmTq/D/Q/39wqbfygGA046HXuevkCLYmw+6b5Hmk8+Ra+D5e7GJqdEUYxXcL6/Kp+xEEKkEp3eQK0Zf71X2PxDAZztLTg+osFnNVEl5vs7XfW5OXXqFG5ubvHWubu78/333390n6ioKKKiouKeh4SEJFueiwEXP1rYABgUBX8TEy5amFM5Mgre+cO8csl2fvFvBgh5buyLU6C22mGEECJTOPsw8KOFDYAB8AuO5OzDQKoXypYqmdJVcePv74+Tk1O8dU5OToSEhBAREYGlpeV7+0ybNo2JEyemSJ5X4a8Stp32f64iaEzlqsKn6HWgj0n6/om+4iOEECKpAkIjMScGBYjE9D+3Sy3pqrhJilGjRjFkyJC45yEhIbi4uCTLsR2tHBO2nU73f086bZarCp/y8Bj83iLp+9s4fXobIYQQyeL50yd8aXGDYL0F+6KLYODDTU85bC1SLVO6Km6cnZ15+TL+b+UvX77Ezs7ug1dtAMzNzTE3N0+RPBVyVMDJyomA8AAMfLjrktZgwFqvJ64/SL4aKZIlQ8lXw/hZhfjBRz7XD5PPWAghUkusTs/s1VsJe/Q31grEKFosiCECs3jb/dPnpkoBh1TLlq4G8atevToHDx6Mt27//v1Ur15dlTxajZaRVUYCoPy7UjUYwGBApyh0yumMr601Bvdp0iSVEBqt8bZugI/8BvBhBmgyXT5jIYRIYY/9A/nRawERj/9Go0CQRU52RBUn8gOFDcB4jxKpOt6NqsXNu3fvuHz5MpcvXwaMt3pfvnyZJ0+eAMYmpc6dO8dt/9133/HgwQOGDx/OrVu3WLBgAb6+vgwePFiN+AC45XNjdr3Z5LDKEW+9s07H5FdvqBMeQbRGYXJ2B4a/Ps676HcqJU1nSnwBnqvA7l9jI9jlhhoDjVdoPsQ+T8pnE0KITGz78cssWrwI66g3xBo05CpbmzkjevHrN5Vxto/f9ORsb8HCbyqk+jg3qt4KfvjwYerXr//e+i5durBy5Uq6du3Ko0ePOHz4cLx9Bg8ezI0bN8iTJw9jx45N1CB+yXkr+P/62AjFemtHVoXd55dLvxJriMXF1oVZdWdRIluJZDt3hpbQEYovrDQO7JenCnTfC5p0dVFSCCHSPJ3ewNwDt3l2YitZNJGEaazwbN2GSsXzx9smpUYoTsz3d5oZ5ya1pFRx8ylXXl1h2JFh+IX5YaoxZWilobQv1h5FSf1hqTOkkBfwayWICYNWi6FsO7UTCSFEhvEyJJJBGy5x+kEgWZVwmueMYHgPT+ysU6+TcGK+v+XX21RS1rEsGz02Ut+lPjH6GKadncaQw0MIiU6+cXcyNbtcUGeocXn/OIiUz1UIIZLD5iMX+G7uJk4/CMTKTMt4zxr8NLBzqhY2iSXFTSqyN7fnl/q/MKLyCEw0Jhx4cgDP7Z5cfXVV7WgZQ/V+4FDQ2FR11EvtNEIIka5Fx8QyZYkvVw7toLT+ARUdFXYMqEXL8rnVjvZJUtykMkVR+KbEN6xuuprcNrl5/u45nfd0ZtX1VWSyFsLkZ2JuvFsK4PRCeH1X3TxCCJFO3X0WwBgvb2Jf3ERRgGz5+b1vQwo62qgdLUGkuFFJqeyl2OixkUb5GhGrj8XrvBcDDw0kOCpY7WjpWxF3KNzYOMLxnpHGW/KFEEIk2Ka/zrJi2RKsY4KIMWgpULkBkwZ0xsYyZcaMSwlS3KjI1syWn+v+zOiqozHVmHL46WFab2/N5YDLakdL39ynGae5uHcA7iRsQlUhhMjsYnR6Jv/my/VjuzEnljCtLW07d6Nzs/Q3qr4UNypTFIV2xdqxttla8trmxT/Mn657urL82nL0Br3a8dKn7K7G/jdgvHoTk3rzmQghRHr07G04notPcfpJKAAGR1fGD+1H6YJpv3/Nh0hxk0YUz1YcXw9fmhZois6gY86FOfQ72I/AyEC1o6VPdYaCjTO8fQSnvdVOI4QQadauK09p9ssxLj0J4olJbkrU/YIJfTtibZF+mqH+TYqbNMTa1JoZtWcwvvp4zLXmHH9+nDbb2nDh5QW1o6U/5rbQaJJx+egsCH6ubh4hhEhjIqJimLhgLfv/XEd4ZBRlXbKwa2Ad2tQrr3a0zybFTRqjKAqti7RmbbO15LfLT0BEAN33due3v3+TZqrEKuMJLlUhJtw49o0QQggArj94wXivX+HVPbJoIulcwoyNvavj4mCldrRkIcVNGlXUoSg+LXzwKOiB3qDn10u/8t3+73gd8VrtaOmHokDTmYBinJrh8Um1EwkhhOrW7DnB+lXLsdaFEo2W4jWbMK5zE8xMMk5JkHHeSQZkZWrF1NpTmVxzMhZaC075naLN9jac8TujdrT0I1c5qNjFuLxruHFOKiGEyITeRUQxfv5q7p85gKmiI8zUns7deuLpVlXtaMlOipt0oKVrSza02IBrFldeR7ym576eLLi8AJ18USdMg7FgYQ8vrxon2BRCiEzm4eswhs1dg+bNAwC0OYsxaWg/iuZ1UjlZypDiJp0olKUQ65qv46vCX2HAwMIrC+m1vxevwl+pHS3ts84O9Ucbl/+aDOFyB5oQIvPYevk5LeYd43BwNkKwonTd5ozp1RYLM1O1o6UYmRU8HdrxYAeTTk0iIjYCBwsHptWeRo1cNdSOlbbpYmFxbQi4AZW/heY/q51ICCFSVGh4JNM3HGTtHePzKgUc+KVtOXJmsVQ3WBLJrOAZXIuCLfBp4UORrEUIjAzku/3fMe/iPGL1sWpHS7u0JtB0hnH5/HLwl8lKhRAZ14Xbj5n883zMnp6ngDaQAQ1cWfdt1XRb2CSWFDfpVAH7AqxtthbPIp4YMLDk6hJ67O2Bf5i/2tHSrgJ1oERLMOhh9wiZd0oIkSEt3XqILetXYa0PIwpTBjcpxQ+Ni2KizTxf+ZnnnWZAFiYWjK0+Fq86XlibWnMx4CJttrfh6LOjakdLuxr/BCaW8PgEXPtD7TRCCJFsgt6FM+aXFTy/fBQTRU+YmQM9e/bii9rpf1C+xJLiJgNoUqAJvi18Ke5QnKCoIPod7Mfs87OJ0ceoHS3tyeICtYcYl/eNhegwdfMIIUQyOHfzIVPneGMa9AS9ASxcSjNlWF8K5MqudjRVSHGTQeS1y8uaZmvoUKwDACuur6Dbnm74vfNTOVkaVGMAZMkLoS/g2Gy10wghRJIZDAZ8zj1h5PpTWOvDicSUKo1aMqL7V5iaaNWOpxopbjIQM60Zo6qOYk69Odia2nLl1RVab2/NoSeH1I6WtphagvtU4/LJeRD4QN08QgiRBKGRMQz2ucyIP65yP9qel1lL0afPdzSvWVbtaKqT4iYDcsvnhq+HL6WylSIkOoSBhwYy4+wMYnTSTBWnWAsoWA900bB3tNpphBAiUU5evc94r/nsv/IIrUZhmHtR5vf/irw5HNSOliZIcZNB5bHNw6qmq+hcojMAa26uofPuzjwLfaZysjTin3mnNCZwexfcPaB2IiGE+CS9Xs+CjfvY88da7PUh1LZ8wYZe1ehX3xWNRlE7XpohxU0GZqo1ZVjlYfza4FfszOy49uYants9OfBYvsgBcCwKVXobl/eMhNhodfMIIcR/eB38jtFzlvHqxim0ioFwS0fG9ulI5fxytebfpLjJBOq51GOTxybKOpYlNCaUwYcHM/XMVKJ0UWpHU1+9EWDtCG/uwtnFaqcRQogPOnblLl6/eGPx7gV6g4JtoQpM/eE7cjtmUTtamiTFTSaR0yYnK5qsoFupbgCsv7WeTrs68STkicrJVGZhDw3HG5cPz4BQGQRRCJF2GAwGFu04xf7N67EyRBKBOXVatGbINx5oM9GgfIkln0wmYqoxZUjFISxouICs5lm5GXgTzx2e7H64W+1o6irXEXJVgOhQODBR7TRCCAFAcEQMfdZcxOv4a4INFkRYOfF9/z40rFRC7WhpnkycmUm9DHvJ8KPDuRhwEYDWRVozovIILEwsVE6mkmfnYWlD43KPA+BSWd08QohM7ejf9/lx1yOeBUViqlUY7laAHnWLotFk3msSMnGm+CQnayeWuS+jV5leKChsurOJjrs68jD4odrR1JGnkvEKDsDuYaDXq5tHCJEp6fV65q7dwYE/15Lt3QNcHCzZ9F0NetYvnqkLm8SSTyoTM9GYMKD8ABY1WoSDhQN33t6h7Y62bL+/Xe1o6mg4Hsxs4cUluLxG7TRCiEzG/00wP85aTPC9C2gVA8Wzwvb+tSjrkkXtaOmOFDeCGrlqsMljE1WcqxARG8GPx39k7ImxRMRGqB0tddk6Qb2RxuUDEyEiSNU4QojMY/+568ydvxDLiAB0BoVsxaoydXAPsliZqR0tXZLiRgDgaOXIb41+o2/ZvigobLm3hfY72nPv7T21o6WuKr0gexEIfw1HZqidRgiRwel0en5evY3jOzdhSRThigWNW7Wnf9sm0gz1GeSTE3G0Gi19yvVhaeOlZLfMzv3g+7Tf2Z7NdzeTafqdm5hBk+nG5TOLIeCWunmEEBnWm3dR9F52hKD7l9EoEGmbm+GD+lGrbGG1o6V7UtyI91TJWYVNHpuokasGkbpIxp0cx+jjowmPCVc7WupwbQhFm4NBB7uHQ2Yp7IQQqebMgzc0m3eMAw/CuaDLR45SNZjyfXey2duoHS1DkFvBxUfpDXqWXV3G/Mvz0Rv05LfLz6y6syjqUFTtaCkv8CF4VwVdFHiuhhJfqJ1ICJEB/NMMtelOFAF6Gwo6WuPdoQLFc8r30afIreAiWWgUDT3L9GS5+3JyWOXgUcgjOu7qyMY7GzN+M5VDAag50Li8dzTEZLLO1UKIZPfYP5AfvRYQ8fgKdUwf8FVZJ7b3ryWFTQqQ4kZ8UkWnimzy2ETt3LWJ0kUx6dQkRhwdwbvod2pHS1m1BoNdbgh+AifmqZ1GCJGO7Th+mUWLF2EV9YZYg4ai5aoyu30lrM1N1I6WIUlxIxIkq0VW5jecz5CKQzBRTNj9aDdtd7Tl5pubakdLOWbW0Hiycfn4bAjK5PNwCSESLSZWx7Rlf3D+wFYsiCFMY8WXbTvRs1UDtaNlaFLciATTKBq6lerGiiYryGmdkyehT+i4qyPrb63PuM1UJb+CfLUgNhL2jVE7jRAiHXn2KpjRXt5EP7uGokBMlryMGTKASsXzqx0tw5PiRiRauRzl2OixkXou9YjRxzD1zFR+OPIDIdEhakdLfooCTWeAooEbW+HBEbUTCSHSgaN3XvHlwjMERuiJMWhwKV+XnwZ1w846k87fl8qkuBFJYm9uz7z68xheeTgmGhP2P96P53ZPrr2+pna05OdcCir1MC7vHgG6WHXzCCHSrOiYWGbuvkaXFWd5Ex6Df5bSfNWhC92/qKd2tExFihuRZIqi0KlEJ1Y3XU1um9w8f/ecTrs7sfrG6ozXTFX/R7B0gFc34dxStdMIIdKgu88CGOPlzdWThzEYoEPVvGwaUI8KRfKqHS3TkeJGfLZS2Uvh6+FLo3yNiNXHMvPcTAYeGkhwVLDa0ZKPlQM0HGtcPjQVwl6rm0cIkab88dc5VixbgnVMEPm0Qcz6wpWprUpjYapVO1qmJMWNSBZ2Znb8XPdnfqz6I6YaUw4/PUyb7W24HHBZ7WjJp0IXcC4NUcFwcJLaaYQQaUBkdAyTF2/g2rFdmBNLmNYWz2+60rpGJhjsNA2T4kYkG0VRaF+sPWubrSWvbV78wvzotqcbK66tQG/Qqx3v82m00NTLuHxxFby4pG4eIYSqbj3xZ5yXN3r/2wAYHF0ZP7QfZVzzqJxMSHEjkl3xbMXxaeFD0/xNiTXEMvvCbPof7M/byLdqR/t8+apDaU/AALuGgz4DFG1CiETbe82PJStWYR0bTLRBS+FqjZjQtyPWFuZqRxNIcSNSiI2ZDTPqzGBc9XGYa8059vwYrbe35sLLC2pH+3yNJoKpNTw7C1d91U4jhEhF0bF6Jm2/Qe81FzkVlYd3JnZ07NKdDu411I4m/ocUNyLFKIpCmyJtWNtsLfnt8hMQHkCPvT1Y8veS9N1MZZcL6gw1Lu8fB1Gh6uYRQqSK6w9f0H3edpafeAhA0xrl+Gn4QEoUyKVyMvFvUtyIFFfUoSg+LXzwKOiBzqBj3qV5fLf/O95EvFE7WtJV7wcOBeHdSzjqpXYaIUQKW7vnBOt/X06+kKvkstSxpHMlxrYogbncDZUmSXEjUoWVqRVTak1hUo1JWGgtOOV3itbbW3PW76za0ZLGxByaTDcun1oAr++qm0cIkSLeRUQx3ns1984cwFTREWNqw4puVWlUwkntaOI/SHEjUo2iKLQq3Ir1zddTyL4QryNe03N/TxZeXohOr1M7XuIVcYfCjUEfA3tGQkYbuFCITO7ve8+Y9PN8NK8fAKDJWZRJw/pRNK8UNmmdFDci1blmdWV9i/W0cm2F3qBnwZUF9N7fm1fhr9SOlnju00BjCvcOwJ29aqcRQiST33cew3fNSqx174jChFJ1mjG2VzsszEzVjiYSQIoboQpLE0sm1ZzE1FpTsTSx5Iz/GVpvb83JFyfVjpY42V2hel/j8p6REBulbh4hxGeJjNEx6s+r7Dx9DVNFR5hpFrr36MnX9SurHU0kghQ3QlUehTzY0GIDRbIWITAykO/2f8e8i/OI1aejySnrDAMbZ3j7EE7NVzuNECKJ7r4MpaX3CdaffcKF2DyYFajIT8P64Zonh9rRRCJJcSNUV9C+IGubraVNkTYYMLDk6hJ67O3By7CXakdLGHNbaPT/p2M4OguCn6ubRwiRaMu2HWbWwmXc9g8hu40ZK7tXY1TnFpiZmqgdTSSBFDciTbAwsWBc9XHMrDMTa1NrLgZcpM32Nhx7dkztaAlTxhPyVIGYcDgwXu00QogECnoXzphfVvDs0hFyKcG4O0ewa2Btahd2VDua+AxS3Ig0pWmBpvi28KW4Q3HeRr2l78G+zL4wmxh9jNrR/puiQDMvQIGrG+FxOus7JEQmdO7mQ6bO8cY06AkGA5i7lOLX/l+Rw85C7WjiM6le3Hh7e5M/f34sLCyoWrUqZ8/+97gnc+fOpWjRolhaWuLi4sLgwYOJjIxMpbQiNeS1y8vqZqtpX6w9ACuuraDbnm74vfNTOdkn5CoHFbsYl3cNh/R4e7sQmYBer+e3zQfZ5rMGa304kZhSqdGXjOz+NaYmMihfRqBqcePj48OQIUMYP348Fy9epGzZsri7uxMQEPDB7detW8fIkSMZP348N2/eZNmyZfj4+PDjjz+mcnKR0sy15vxY9Udm15uNraktV15dofX21hx6ckjtaP+twViwsIeXV+HCSrXTCCH+JSwqltELfPH7+zgmip5w82z06fMdLWqWUzuaSEaKwaDeyGNVq1alcuXKzJ9vvMNEr9fj4uLCgAEDGDly5Hvb9+/fn5s3b3Lw4MG4dT/88ANnzpzh+PHjCTpnSEgI9vb2BAcHY2dnlzxvRKSop6FPGX5kONfeXAOgU4lODK4wGFNtGh1v4sxi2D0cLLPCgItg5aB2IiEEcNMvhH7rLvLm9WtamN8iS4FSDPnmC7Ra1RsxRAIk5vtbtb/R6OhoLly4gJub2/+F0Whwc3Pj1KlTH9ynRo0aXLhwIa7p6sGDB+zatYtmzZp99DxRUVGEhITEe4j0xcXWhVVNV/FN8W8AWH1jNV32dOFZ6DOVk31EpR6QowREvIVDU9ROI0Smp9frWbH/El96n+DBqzAsbbPStF13hnVpKYVNBqXa3+rr16/R6XQ4OcUfxtrJyQl/f/8P7tOhQwcmTZpErVq1MDU1pVChQtSrV+8/m6WmTZuGvb193MPFxSVZ34dIHaZaU0ZUGcG8+vOwM7Pj6uureG735MDjA2pHe5/WBJrOMC6fXw7+V9XNI0Qm9jr4HWPmLufRiW1k1QdTr6gjuwbVplYxmck7I0tXJevhw4eZOnUqCxYs4OLFi/z555/s3LmTyZMnf3SfUaNGERwcHPd4+vRpKiYWya1+3vps9NhIWceyhMaEMvjwYKaemUq0LlrtaPEVqAMlWoJBD7tHyLxTQqjg+JW7eP3ijXmoceypdqWzsLxLZRyszVROJlKaan1uoqOjsbKyYtOmTbRs2TJufZcuXQgKCmLr1q3v7VO7dm2qVauGl5dX3Lo1a9bQq1cv3r17h0bz6VpN+txkDDH6GH699Csrrq0AoLhDcWbVnUVeu7wqJ/sfQU9gfhWIjYDWy6HU12onEiJT0Ov1LNi4j4CbZ9EqBiIwx63ZF7hVLqF2NPEZ0kWfGzMzMypWrBivc7Ber+fgwYNUr179g/uEh4e/V8Botcbb9lTsFy1UYKoxZUjFIXg39CaLeRZuBt7Ec4cnex7tUTva/8mSF2oNNi7vGwvRYermESITePk2lNGzl/Dm1hljYWPpxPf9+0hhk8mo2iw1ZMgQlixZwu+//87Nmzfp06cPYWFhdOvWDYDOnTszatSouO09PDxYuHAhGzZs4OHDh+zfv5+xY8fi4eERV+SIzKVOnjps9NhIhRwVCIsJY9iRYUw6NYnI2DQy9lHNgcYiJ+Q5HJutdhohMrQrT4MYsHAHFmH+6AwK9oUrMXVoL5yz2asdTaQyVSfNaNu2La9evWLcuHH4+/tTrlw59uzZE9fJ+MmTJ/Gu1IwZMwZFURgzZgzPnz/H0dERDw8PpkyRO1IyM2drZ5a5L2PB5QUsvbqUjXc2cuXVFWbVnUUB+wLqhjO1BPep4PMNnJwH5TuCQ0F1MwmRwRgMBpafeMT03TeJ0dliY5Obzs1qUa9CMbWjCZWoOs6NGqTPTcZ28sVJRh0bRWBkIJYmloytNhaPQh7qhjIYYHVLeHAYijaH9uvUzSNEBuL/Jpiff/+TLQEOxGBC01LOTP+6DPaWaXQcLJFkifn+luJGZDivwl8x8thIzvobx0Nq5dqKUVVHYWliqV6ogFuwqCboY+GbP8DV7dP7CCH+04FzNziwaxuWRPFQl40GTVvQqVo+FEVRO5pIAemiQ7EQKcXRypHfGv1Gn7J9UFDYfG8zHXZ24H7QffVC5SgGVXobl3ePhNg0duu6EOmITqdn9uptHNu5CUuiCFcs6PG1O52r55fCRgBS3IgMSqvR0rdcX5Y0XkJ2y+zcC7pHux3t2HJvi3qh6o0Aa0d4cxfOLlYvhxDp2PNXQfw4axGhDy6hUQxE2uRi2KB+1C5bWO1oIg2R4kZkaFVzVmWjx0aq56xOpC6SsSfGMvr4aMJjwlM/jIU9NBxvXD48A0Jfpn4GIdKxAxduM3/BQqwiX6EzKOQoWYMpg3uQ3d5G7WgijZHiRmR42S2zs6jRIgaWH4hG0bDt/jba7WzHnbd3Uj9MuY6QqwJEh8KBCal/fiHSIb3egPehewz84xY6g4FwxZKmrTvSp3WjBA3eKjIf+VchMgWNoqFnmZ4sd19ODqscPAx+SIedHdh0Z1PqDgCp0UCz/z/C9pV18PRc6p1biHToxZsQuqw4i9fe24TrTdAXrMXIwf2pXqqQ2tFEGibFjchUKjpVZJPHJmrlrkWULoqJpyYy4ugI3kW/S70QeSoZr+AA7B4Gen3qnVuIdGTHicv8On8+fg9uYWGqYWbrMnh1qk1WWyu1o4k0ToobkelktciKd0NvhlQcglbRsvvRbtruaMvNNzdTL0TD8WBmCy8uweW1qXdeIdKBmFgd05f/wbn9W7EghnIWb9jaryaelVzkbiiRIFLciExJo2joVqobK5usxNnamSehT+i4qyMbbm1InWYqWyfj3VNg7HsTEZTy5xQiHXj44jWjvRYQ9fQaGgVisuRl7OA+FHWWcclEwklxIzK1cjnKscljE/Vc6hGjj2HKmSn8cOQHQqNDU/7kVXpD9iIQ/hqOzEj58wmRxm09epElS37DOjqQGIOGPOXq8NOgbtjbqDgAp0iXpLgRmZ69uT3z6s9jWKVhmGhM2P94P222t+Ha62spe2ITM2gy3bh8ZrFxFGMhMqFYnZ6ZWy9w8a/tmBNDmMaaVu070+PL+mpHE+mUFDdCAIqi0LlkZ1Y1WUVum9w8f/ecTrs7sebGmpRtpnJtaJxvyqCD3cON81AJkYn4BUfQYckZFpzy52qsMzqHAoz9oT8Vi+ZTO5pIx2RuKSH+JSQ6hPEnxnPgyQEA6rvUZ3LNydib26fMCQMfgndV0EWB52oo8UXKnEeINOaPQ+eYfdSP5xFabMxNmNKqFF+Wy612LJFGydxSQnwGOzM7ZtebzagqozDVmHLo6SHabG/DlVdXUuaEDgWg5kDj8t7REBORMucRIo2IjI5h8uINXDu6i/L625TKacP2AbWksBHJRoobIT5AURQ6FO/AmmZrcLF1wS/Mj667u7Ly2kr0hhQYl6bWYLDLDcFP4MS85D++EGnE7ScvGefljd7/NgDZczizoVc1CmS3VjmZyEikuBHiP5TIVgLfFr40yd+EWEMsP1/4mQF/DeBt5NvkPZGZNTSebFw+PhuCniTv8YVIA3wOnGHViiVYxwYTbdDiWtWNCf06YWNprnY0kcFIcSPEJ9iY2TCzzkzGVR+HmcaMo8+O0mZ7Gy6+vJi8Jyr5FeSrBbGRsG9M8h5bCBVFRMUwceE6bp3Ygxk6wrR2dOjcnY5NaqodTWRQUtwIkQCKotCmSBvWNV9Hfrv8vAx/Sfe93Vl6dWnyNVMpCjSdAYoGbmyFB0eS57hCqOhpYDjtl5zmtf9z44ochZk4rD8lC+ZSN5jI0KS4ESIRijoUxaeFDy0KtkBn0PHLxV/oc6APbyLeJM8JnEtBpR7G5d0jQBebPMcVQgW7r76g2bxjXH4WwnlNUYrVcGd8nw5YmpuqHU1kcHIruBBJYDAY2HJvC1PPTCVSF4mjpSMz6sygsnPlzz94eCD8WgEi3kLTmVC19+cfU4hUFBYZhdeyjVzxC+dSbG7K583Cr+3LkyerTHgpkk5uBRcihSmKQqvCrVjffD2F7AvxKuIV3+77loVXFqLT6z7v4FYO0GCscfnQFAh7/fmBhUglf997xsRZ3iiv71PGxI9eVR3x7V1dChuRqqS4EeIzuGZ1ZV3zdbR0bYneoGfB5QX03t+b1xGfWZBU7ArOpSEyGA5OSpasQqS0VbuO4btmJda6UKIwoXSdZvzYqgqmWvmqEalLmqWESCbb729n8unJRMRG4GDhwPTa06meq3rSD/j4FKxoAijQ6xDkKp9sWYVITqHhkXgt80Ub+BCAMNMs9OzcnsJ5cqicTGQk0iwlhAo8CnmwocUGCmctTGBkIL339+bXS78Sq09ip+B81aF0G8AAu2TeKZE23QsIZcLshWgDH2IwgGmuEvw0rJ8UNkJVUtwIkYwK2hdkXbN1tC7SGgMGfvv7N77d9y0vw14m7YCNJoGpNTw7C3/7JG9YIT7T5kvP+GL+Ca5EOBCFCeUatODHnm0wMzVRO5rI5KS4ESKZWZhYML76eGbWmYmViRUXXl6gzfY2HH9+PPEHs8sFdYYal/ePg6jQ5A0rRBKEhEUyYu1xBvtcITxah2O+ovTp04+WdSqqHU0IQIobIVJM0wJN8fXwpbhDcd5GvaXPgT7MuTCHGH1M4g5UvR9kLQDvXsJRr5QJK0QCnb/5iJ9m/4rhzlEslFgGNSzMmm+r4pIji9rRhIgjxY0QKSifXT5WN1tNu6LtAFh+bTnd93THP8w/4QcxMYcm043LpxbA63spkFSIT1uy+S+2+qzGWh+OVgNzWxZmcKMiaDWK2tGEiEeKGyFSmLnWnNHVRvNz3Z+xMbXh8qvLtN7emsNPDyf8IEXcwbUR6GNgz0jpXCxS1dvQcEbPXc6Lv49hougJN8/Gd72/o0nVEmpHE+KDpLgRIpU0zt8YXw9fSmYrSXBUMAP+GoDXOS9idAloplIU49UbjSnc2w939qZ8YCGAU9cfMH2ON2bBT9EbwDJfGaYO60s+Zwe1ownxUVLcCJGKXGxdWN10Nd8U/waAVTdW0WVPF56FPvv0ztldoXpf4/KekRAblYJJRWZnMBhYd+YJi313YWUIJxIzqrm3YnjXVmhlUD6Rxsm/UCFSmanWlBFVRvBL/V+wNbPl6uureG735ODjg5/euc4wsHGCtw/hlHfKhxWZUmhkDAM3XObHzVc5EeVCqHUe+vX5jqbVy6gdTYgEkRGKhVDRi3cvGHZ0GH+/+huADsU68EOlHzDTmn18pysbYHNv4/g3A84bbxcXIpkcv3KXFTuOcjDUCa1Gw3D3ovSsXRCNdBoWKpMRioVIJ3LZ5GJlk5V0K9kNgHW31tFpdyeehjz9+E5l2kKeKhATZhz7RohkoNfrme+zh32b15M39hmVbELw7V2d3nULSWEj0h0pboRQmanGlCGVhuDd0Jss5lm48eYGbXa0Yc+jPR/eQVGg2UxAgasbjXNQCfEZXgWHMnr2Ut7cOoNWMRBhmYP5fZpTMV9WtaMJkSRS3AiRRtTJU4eNHhupkKMCYTFhDDsyjMmnJhOl+0DH4VzloUJn4/LuYaDXpW5YkWEcvniLWb8swCLMD51Bwd61IlOH9sY5m73a0YRIMiluhEhDnK2dWea+jJ6le6Kg4HvHl447O/Io+NH7GzccBxb24H8VLqxM7aginTMYDMzz3cdf23yxMkQSoZhT/wtPvu/YAo1GvhpE+ib/goVIY0w0JgysMJBFbotwsHDg9tvbeO7wZMeDHfE3tM4O9Ucbl/+aDOGBqR9WpEtB4dH0Wn2BtZcDUTAQYeXM4AH9qF+hmNrRhEgWUtwIkUbVyF2DjR4bqexcmYjYCEYdG8X4k+OJiI34v40q9QDH4hDxFg5NVS+sSDfO3PWn+bzj7L/xkreKPbmrNmfqDz1xymqrdjQhko0UN0KkYTmscrCk0RL6lO2DgsKfd/+kw84O3A+6b9xAa/L/OxcD55eB/zX1woo0TafTM3vNdrauXcq74EDyZbPiz7416NW0kjRDiQxH/kULkcZpNVr6luvLksZLyG6ZnXtB92i/sz1b7m0xblCgDpRoCQY97B4u806J9zx/FcSPPy8i9P5FzBUd7jmj2DGgFqVyS6dhkTFJcSNEOlE1Z1U2emykWs5qRMRGMPbEWEYfH014TDg0ngwmlvD4BFz/U+2oIg3Ze+Yavy5YiFXEK3QGBccS1Znevz22FqZqRxMixUhxI0Q6kt0yO4vcFjGg/AA0ioZt97fRbmc77hgiodZg40b7xkJ0mLpBhep0Oj1ev2/h5O4/sCSacMWSJl93pG+bxtIMJTI8+RcuRDqj1WjpVaYXyxovI4dlDh4GP6TDzg784ZQXQ5a8EPIcjs9RO6ZQ0et3UQzw3kz4oytoFIi2y8PIwf2pUbqQ2tGESBVS3AiRTlVyrsTGLzZSM3dNonRRTDg7lRGFyhCmKHBiHgQ+VDuiUMGp+29o9ssxdr8wx09vR87StZg8qBtZba3UjiZEqpGJM4VI5/QGPSuvr2TexXnoDDryYcqs508oVqAxtF+ndjyRSmJidczZsI/frscSa9BQOIcN8zuUp6iz/JwTGYNMnClEJqJRNHQv1Z2VTVbibO3MY2LomMsZnxdHMNzdr3Y8kQoe+b9mtNcCIu6fpbzJc9pUzMPW/jWlsBGZ1mcXN1FRH5j3RgiR6srlKMfGFhupl6ce0YrCT9kdGHpkKKHhb9SOJlLQtmOXWLz4N6yjA4k1aGhSqShebcpiZWaidjQhVJPo4mb37t106dKFggULYmpqipWVFXZ2dtStW5cpU6bw4sWLlMgphEiALBZZmNdgHsPKDcDEYGCfqR7PLR5cf31d7WgimUXHxDJ16UYuHtyGBTGEaaxp2b4zPb6sr3Y0IVSX4D43mzdvZsSIEYSGhtKsWTOqVKlCrly5sLS0JDAwkGvXrnHs2DFOnTpF165dmTx5Mo6OjimdP9Gkz43ILK4en8mwWyt4bmqCiWLCD5V+oGPxjiiKonY08ZnuP3/F4t/XYx3zFoDYrPkZ/m1bbK0sVE4mRMpJzPd3goub6tWrM2bMGJo2bfqfYyQ8f/6cX3/9FScnJwYPHpy45KlAihuRaej1hCytx7jY5xy0Nt4p08ClAZNqTsLeXEamTa8O3w5grM9p6uivoACulevStXkdtWMJkeJSpLjJKKS4EZnKs/MYljZkva0NsxxzEGOIJZd1LrzqelHGsYza6UQiRMfqmL3/LouOGOcVq549hpEtK1HWNY/KyYRIHal2t1R0dDS3b98mNjb2cw4jhEgpeSqhlOtIh9B3rI6yxsXWhRdhL+iyuwu/X/8dvUGvdkKRAHeevmT0zPlsPXYJgM7V87FikIcUNkJ8RJKKm/DwcHr06IGVlRUlS5bkyZMnAAwYMIDp06cna0AhxGdqOB7MbCn5/Cq+Ll/hnt+dWEMss87PYsBfAwiKDFI7ofgPvgfO8PvypdjEBFHT7AneHcox6ctSWJhq1Y4mRJqVpOJm1KhRXLlyhcOHD2Nh8X8d2Nzc3PDx8Um2cEKIZGDrBPVGAGBzaBpelUczttpYzDRmHH12lNbbW3Mp4JLKIcW/RUTFMGnhem6e2IMZsYRpbenWuRPNy+RWO5oQaV6SipstW7Ywf/58atWqFe/Oi5IlS3L//v1kCyeESCZVekO2whD+GuXoTDyLerKu+Try2+XnZfhLuu3pxtKrS6WZKo248ciP8bPmYwi4Y1zhWJgJQ/tTsmAudYMJkU4kqbh59eoVOXLkeG99WFhYom8z9fb2Jn/+/FhYWFC1alXOnj37n9sHBQXRr18/cubMibm5OUWKFGHXrl2JOqcQmY6JGTSdYVw+sxgCblHUoSgbWmygecHm6Aw6frn4C30P9OVNhAz6p6atZ++xduUyrGNDiDZoKVq9MeP7dsDKwkztaEKkG0kqbipVqsTOnTvjnv9T0CxdupTq1asn+Dg+Pj4MGTKE8ePHc/HiRcqWLYu7uzsBAQEf3D46OppGjRrx6NEjNm3axO3bt1myZAm5c8tlWiE+ybUhFG0OBh3sHg4GA9am1kyrNY1JNSZhobXgxIsTtNnehnP+59ROm+lExeqYsO06g/68xRNdFsJM7PmmWw/aNU74z1QhhFGSbgU/fvw4TZs25ZtvvmHlypX07t2bGzducPLkSY4cOULFihUTdJyqVatSuXJl5s+fD4Ber8fFxYUBAwYwcuTI97ZftGgRXl5e3Lp1C1NT08TGBuRWcJHJBT4E76qgi4K2a6C4R9xLd9/eZeiRoTwIfoBG0fBd2e/oVboXWo10XE1pVx88Z+z2O1z2CwegV828DHEvhoVZ0n7OCZERpco4N/fv32f69OlcuXKFd+/eUaFCBUaMGEHp0qUTtH90dDRWVlZs2rSJli1bxq3v0qULQUFBbN269b19mjVrhoODA1ZWVmzduhVHR0c6dOjAiBEj0Go//AM4Kioq3vxXISEhuLi4SHEjMq+Dk+HYLMiSF/qdBVPLuJfCY8KZemYqW+8b//9VzVmV6bWnk90yu1ppM7xVu45x5+wR/PQ2XNQW5+e25WhQzEntWBmKTqcjJiZG7RgiAczMzD46UHBiipsEz6w2ZMgQJk+ejLW1NUePHqVGjRosWbIkcan/x+vXr9HpdDg5xf9P7OTkxK1btz64z4MHD/jrr7/o2LEju3bt4t69e/Tt25eYmBjGjx//wX2mTZvGxIkTk5xTiAyn9hC4sh6CnsCJeXF3UgFYmVrxU62fqJKzCj+d/okzfmdova010+tMp1rOaiqGznjeRUQxc6kP2sCHmCqQzUJhy7dVyZcji9rRMgyDwYC/vz9BQUFqRxEJpNFoKFCgAGZmn9fHLMFXbkxNTXn27BlOTk5otVr8/Pw+2Kk4oV68eEHu3Lk5efJkvH46w4cP58iRI5w5c+a9fYoUKUJkZCQPHz6Mu1Ize/ZsvLy88PPz++B55MqNEB9w7Q/Y1B1MLKD/OeNVnH95EPyAoUeGcvftXRQUepXpxXdlv8NEI7NNf67Ld5+wbsNGrPXvADDJVZxhXb/CzFQ+2+Tk5+dHUFAQOXLkwMrKSuZVS+P0ej0vXrzA1NSUvHnzvvf3lSJXbvLnz8+8efNo3LgxBoOBU6dOkTVr1g9uW6fOp+c5yZ49O1qtlpcvX8Zb//LlS5ydnT+4T86cOTE1NY3XBFW8eHH8/f2Jjo7+YKVnbm6Oubn5J/MIkamU/ArOLYPHJ2DfWPD8/b1NCtoXZF2zdUw/O50/7v7B4r8Xc/7leWbWmUkOq6T/YpPZrdh+hPsXjmKt6InChMr1mtCqbsL6KYqE0+l0cYVNtmzZ1I4jEsjR0ZEXL14QGxub5L61kIi7pby8vFi2bBn169dHURRatWpFvXr13nvUr18/QcczMzOjYsWKHDx4MG6dXq/n4MGDH73jqmbNmty7dw+9/v/G4rhz5w45c+b87EtYQmQqimK8NVzRwI0t8ODIBzezMLFgQo0JzKg9AysTKy68vEDrba05/vx46ubNACKidYzwvcj18ycxVfSEmWXl2569pLBJIf/0sbGyslI5iUiMf77LdTrdZx0nwcVNy5Yt8ff3JyQkBIPBwO3bt3n79u17j8DAwASffMiQISxZsoTff/+dmzdv0qdPH8LCwujWrRsAnTt3ZtSoUXHb9+nTh8DAQAYNGsSdO3fYuXMnU6dOpV+/fol4y0IIAJxLQ6UexuXdI0D38TnimhVshq+HL8UcivE26i19DvRh7oW5xOplXrmEuPsylC+9j+Nz0Y8jMQUxy1OKKcP6UTCXo9rRMjxpikpfkuvvK9ENvDY2Nhw6dIgCBQpgYvJ57cNt27bl1atXjBs3Dn9/f8qVK8eePXviOhk/efIkXq9pFxcX9u7dy+DBgylTpgy5c+dm0KBBjBgx4mOnEEL8l/o/wrVN8OomnF8GVXt/dNN8dvlY02wNXue88Lntw7Jry7gYcJGZdWbibP3hpmQBS7b8xeaLz7gTnQ1HW3N+aVuVGq5y95kQKSnJt4L/IzIykujo6Hjr0nJHXRnnRoh/ObcMdg4BC3sYcBGsP/3Fu+/RPsafHM+7mHfYm9szpeYU6rrUTYWw6cfb0HBmLduAWfBTYg0Kz5xqMfObmjjaSh/A1PDPzScFChSINweiSNv+6+8tMd/fSZ4VvH///uTIkQNra2uyZs0a7yGESEcqdjU2UUUGw8FJCdqlcf7G+Hr4UjJbSYKjgun/V39mnZtFjE7GEgE4ff0B0+d4Yxb8FL0BbPOX5reedaWwSYd0egOn7r9h6+XnnLr/Bp3+s64HJEjXrl3jjf/2b5cuXaJt27Zx0xDly5ePFi1asH37dv65XvHo0SMURYl7mJmZ4erqyk8//cRnXtNIF5JU3AwbNoy//vqLhQsXYm5uztKlS5k4cSK5cuVi1apVyZ1RCJGSNFpo6mVcvrgKXiRshnAXWxdWNV3FN8W/AeD3G7/TdU9Xnr97nlJJ0zy9Xs+iPw6wc+MarAzhRGJGNfdWDO/aClMTGek5vdlzzY9aM/6i/ZLTDNpwmfZLTlNrxl/sufbhoUdSw9atW6lWrRrv3r2L66+6Z88eWrVqxZgxYwgODo63/YEDB/Dz8+Pu3btMnDiRKVOmsHz5cpXSp54kNUvlzZuXVatWUa9ePezs7Lh48SKurq6sXr2a9evXp+mJLKVZSoiP+ONbuLoR8lSBHvuMd1Ql0MEnBxl7Yiyh0aHYmtkyueZkGuZtmIJh057QyBimLViFeegzAMItsjOgWwfy5JCr2Wr43GapPdf86LPmIv/+gvznf8XCbyrQpFTOz875IV27diUoKIgtW7bEWx8WFka+fPmoU6cOf/755wf3NRgMKIrCo0ePKFCgAJcuXaJcuXJxr7u5uVG0aFG8vb1TJPvnUrVZKjAwkIIFCwLG/jX/3CFVq1Ytjh49mpRDCiHU1mgSmFrDs7Pwt2+idm2YtyGbPDZRJnsZQqND+f7Q90w/O51oXfSnd84Arr8I5ov5J7geqEdvAOsC5Zk6tI8UNmmMwWAgPDr2k4/QyBjGb7v+XmEDxK2bsO0GoZExCTpecjUD7du3jzdv3jB8+PCPbvNfdxudP3+eCxcuULVq1WTJk5Yl6XanggUL8vDhQ/LmzUuxYsXw9fWlSpUqbN++nSxZsiRzRCFEqrDLBXV+MPa72T8OijUDc9sE757LJhcrm65k3sV5rLy+krU313Ip4BKz6szCxc4lBYOrR6/Xs+rEPabuvU90rJ5cdgWo17QO9csXVTua+ICIGB0lxu397OMYAP+QSEpP2Jeg7W9McsfK7PNHn75z5w4ARYv+37+vc+fOxRtfbsOGDbRo0SLueY0aNdBoNERHRxMTE0OvXr3o3LnzZ2dJ65J05aZbt25cuXIFgJEjR+Lt7Y2FhQWDBw9m2LBhyRpQCJGKqveHrAXgnT8c9Ur07qYaU36o9APeDb3JYp6FG29u4LnDk72PPv8LJa15FRzK6DlLuXRgC7rYWNyK52DnIClsROoqU6YMly9f5vLly4SFhREbG3/sKR8fHy5fvsyVK1fw9fVl69atjBw5UqW0qSdJpeTgwYPjlt3c3Lh16xYXLlzA1dWVMmXKJFs4IUQqMzGHJtNhfVs4tQDKd4bsrok+TJ08ddjosZHhR4dzKeASQ48M5Zz/OYZVHoa5Nv3fMXTk0m12bd+ClSESM0VhcHUH+n1RSQaMS+MsTbXcmOT+ye3OPgyk64pzn9xuZbfKVCngkKDzJofChQsDcPv2bapVM05ka25ujqvrx/+Puri4xL1evHhx7t+/z9ixY5kwYUKGvkU+SVdu/i1fvnw0aNBAChshMoIi7uDaCPQxsHfUp7f/CGdrZ5a7L+fb0t8C4HPbh447O/Io+FEyBU19er2eeet3cXCrD1aGSCIwp66HJ/2/rCmFTTqgKApWZiaffNQu7EhOews+9jeqADntLahd2DFBx0uufxuNGzfGwcGBGTNmJPkYWq2W2NjY98any2iSVNzMmDEDHx+fuOeenp5ky5aN3LlzxzVXCSHSKUUxXr3RmMLdfXB7T5IPZaIxYVCFQSxyW4SDhQO3396m7Y627HywMxkDp46XgSH8+PNvvL1zDq1iIMLKicED+9KgYjG1o4lkptUojPcoAfBegfPP8/EeJdBqUq6gDQ4Ojmtu+ufx9u1bli5dys6dO2nevDl79+7lwYMH/P3338ycOdOYXRv/KtGbN2/w9/fn2bNn7N69m19++YX69etn+LuFk1TcLFq0CBcXYwfB/fv3s3//fnbv3k3Tpk2lz40QGUF2V6je17i8ZyTERn3W4WrmrslGj41Udq5MeGw4I4+NZMLJCUTERiRD2JR36clbxnuvwTL8JTqDgkPRKkz9oRdOWTP2F0Rm1qRUThZ+UwFn+/hNN872Fil6G/g/Dh8+TPny5eM9Jk6cSKtWrTh58iRWVlZ07tyZokWL0qBBA/7666/3OhODsetIzpw5yZ8/P7169aJZs2bxLk5kVEka58bS0pI7d+7g4uLCoEGDiIyMZPHixdy5c4eqVavy9u3blMiaLGScGyESKCoUfq0I715Cw/FQe8hnH1Kn17Ho70UsvrIYAwZcs7jyc92fKZilYDIETn56vYFlxx8yY88tzA2RNLF6xJceLahTroja0cQnJNf0Czq9gbMPAwkIjSSHrQVVCjik6BWbzE7VcW6yZs3K06dPAdizZw9ubm6AcQyBz52mXAiRRpjbGse+ATg6C0JefPYhtRot/cr147fGv5HNIhv3gu7Rbmc7tt7b+tnHTm4vXgcxaMEWpuy6SazeQL3S+Rk3tL8UNpmMVqNQvVA2viyXm+qFsklhk04kqbj56quv6NChA40aNeLNmzc0bdoUMM538V+9toUQ6UxpT+OIxTFhxrFvkkm1nNXY9MUmquasSkRsBGNOjGH08dGEx4Qn2zk+x76z15jnvZDsb/6mgGkwP7Usxfz25bG3NFM7mhAiAZJU3MyZM4f+/ftTokQJ9u/fj42NDQB+fn707ds3WQMKIVSk0UCzmYBinJrh8alkO3R2y+wsdltM/3L90Sgatt3fRrud7bj79m6ynSOxdDo9s1Zt5cSuP7AkmnDFkimeVfimWj65G0qIdCRJfW7SM+lzI0QSbBsIF383zh7e64hxss1kdM7/HCOPjiQgIgBzrTmjqoziq8JfpWpB8SQgEO8V67GKfA1AlG1uhn7bHgc761TLIJJPcvW5EakrufrcJHk86Lt373Lo0CECAgLQ6/XxXhs3LvkuXwsh0oCG4+D6FvC/aixyKnVP1sNXdq7Mxi828uPxHznx/AQTTk3grP9ZxlUfh7VpyhcXu079zbF9O7AihliDgkuZmnzbsj4aTbIMBSaESGVJunKzZMkS+vTpQ/bs2XF2do7325WiKFy8eDFZQyYnuXIjRBKdXgR7RoClAwy4AFafHpk1sfQGPSuureDXS7+iM+jIb5cfr7peFHNImbFkdHoD3ofuseWv09Q1e0C4xoqvvm5N1RIFUuR8IvXIlZv0Kbmu3CSpuMmXLx99+/ZlxIgRid1VdVLcCJFEulhYVAte3YTKPaH5rBQ71aWASww7MoyX4S8x05gxvPJwPIt6Jmsz1cuQCIb4XuHEvTcAtCukZ2T7hmSxsUq2cwj1SHGTPql6K/jbt29p06ZNUnYVQqRXWhNo+v+HfT+/DPyvpdipyucozyaPTdTNU5dofTQ/nfmJYUeHERodmizH3378EjPmeHPxnj+Wplp+blOW6T09pLARIoNIUnHTpk0b9u1L2FTvQogMpGBdKPElGPSwewSk4P0IWSyy8GuDXxlaaSgmigl7H+3Fc7sn119fT/Ixo2NimbZ0ExcObMOeMOrZvWL7gFp8XTFPMiYXQqgtSR2KXV1dGTt2LKdPn6Z06dKYmprGe33gwIHJEk4IkQY1/gnu7IPHx+H6n1Dq6xQ7laIodCnZhfI5yjPsyDCevXvGN7u/YWiloXQo1iFRzVQPXrxi0cr1WMe8RVEgNms+Zn7bDlsrabIQIqNJ0pWb3377DRsbG44cOcL8+fOZM2dO3GPu3LnJHFEIkaZkyQu1BhuX942F6LAUP2UZxzL4evjSwKUBsfpYpp+dzuDDgwmOCk7Q/n8ePs/SJb9hHfOWGIOGfBXrMXlgVylsxKfpdfDwGFzdZPxTn/Kj8Hft2hVFUZg+fXq89Vu2bIlX0BsMBpYsWUL16tWxs7PDxsaGkiVLMmjQIO7duxe33YQJE1AUJe5hb29P7dq1OXLkSIq/l/+VP3/+VKsRklTcPHz48KOPBw8eJHdGIURaU3Mg2OeFkOdwfE6qnNLe3J659ecysspITDWmHHxyEM/tnvz96u+P7hOr0zN17X6uHtmJObGEaWxo3bELXVvUTZXMIp27sQ3mloLfW8AfPYx/zi1lXJ/CLCwsmDFjxkfnajQYDHTo0IGBAwfSrFkz9u3bx40bN1i2bBkWFhb89NNP8bYvWbIkfn5++Pn5cerUKQoXLkyLFi0IDk7YLwjpjQziIIRIPFNLcJ9iXD4xDwIfpsppFUWhY/GOrG62mjw2eXgR9oIuu7vw+/Xf+feNny+CImj322mWX40gWG+OPltBxg3tT7nCeVMlq0jnbmwD387vz6kW4mdcn8IFjpubG87OzkybNu2Dr/v4+LBhwwZ8fHwYO3Ys1apVI2/evFSrVo0ZM2awYsWKeNubmJjg7OyMs7MzJUqUYNKkSbx79447d+7EbfPkyRO+/PJLbGxssLOzw9PTk5cvX8Y7zsKFCylUqBBmZmYULVqU1atXx71mMBiYMGECefPmxdzcnFy5csV1U6lXrx6PHz9m8ODBcVeQUlKS+tx07/7fA3gtX748SWGEEOlIcQ8oWA8eHIa9o6H9ulQ7dclsJfH18GXCyQnse7yPWedncc7/HD/V/IksFlnYfPxvJh70IygiFltzc+p90Z4vKuZLtXwijTIYICHzl+l1sHs48KEO8wZAMY75VLBewkbrNrWCRH6Za7Vapk6dGnd1Jk+e+J3e169fT9GiRfniiy8+uP9/FQ9RUVGsWLGCLFmyULRoUQD0en1cYXPkyBFiY2Pp168fbdu25fDhwwBs3ryZQYMGMXfuXNzc3NixYwfdunUjT5481K9fnz/++IM5c+awYcMGSpYsib+/P1euXAHgzz//pGzZsvTq1YuePXsm6rNIiiQVN/++TBYTE8O1a9cICgqiQYMGyRJMCJHGKQo0mQELa8DtnXDvALi6pdrpbc1smVV3FhvvbGTG2RkceXaENts8qfT6SyxevSZXdB7y5inK/PYVyJtNbvEWGAubqbmS4UAG4xWd6S4J2/zHF2CW+JG2W7VqRbly5Rg/fjzLli2L99qdO3fiCpN/fP/99yxduhSALFmy8OzZs7jXrl69GjcPZHh4OLa2tvj4+MSNF3Pw4EGuXr3Kw4cPcXExvq9Vq1ZRsmRJzp07R+XKlZk1axZdu3aNm0NyyJAhnD59mlmzZlG/fn2ePHmCs7Mzbm5umJqakjdvXqpUqQKAg4MDWq0WW1tbnJ2dE/1ZJFaSmqU2b94c77Fjxw4ePHhA27ZtqVatWnJnFEKkVTmKQdXexuXdIyE2OlVPrygKnkU9Wdt8LQVMC1H8fjEsXhnnhqqSx5KN31WXwkakazNmzOD333/n5s2bn9x29OjRXL58mXHjxvHu3bt4rxUtWpTLly9z+fJlLly4QJ8+fWjTpg3nz58H4ObNm7i4uMQVNgAlSpQgS5Yscee+efMmNWvWjHfcmjVrxr3epk0bIiIiKFiwID179mTz5s3ExsZ+1vtPqiTPLfVvGo2GIUOGUK9ePYYPH55chxVCpHV1R8DfvvDmLpz9DWr0T/UIl86/pfTdSpihI1oTzfls5ylQ2I+w2CaYmyT/NBEinTK1Ml5F+ZTHJ2Ft609v13ET5KuRsPMmUZ06dXB3d2fUqFF07do1bn3hwoW5fft2vG0dHR1xdHQkR44c7x3HzMwMV1fXuOfly5dny5YtzJ07lzVr1iQ53/9ycXHh9u3bHDhwgP3799O3b1+8vLw4cuTIe0PGpLRk7VB8//591ao0IYRKLLOA2wTj8uHpEPryv7ZOVmGRUUxYsJY7p/Zhho4wEzvyNizGG7s3nHh+gjbb2nDe/3yq5RFpnKIYm4c+9SjUAOxyAR/rt6KAXW7jdgk53md2np0+fTrbt2/n1KlTcevat2/P7du32bp1a5KPq9VqiYiIAKB48eI8ffqUp0+fxr1+48YNgoKCKFGiRNw2J06ciHeMEydOxL0OYGlpiYeHB/PmzePw4cOcOnWKq1evAsYCS6dL+VvpIYlXboYMGRLvucFgwM/Pj507d9KlS5dkCSaESEfKdTROyfDiEhycCC0XpPgpH78JY9iqoxQPvo+igMapCJO6t8bCzJQGxasx9MhQHgQ/oMe+HvQt25dvS3+LNiGdP4XQaI39yXw7Yyxw/rdj8f8vVJpMT1hn4mRQunRpOnbsyLx58+LWtWvXjj///JN27doxatQo3N3dcXJy4vHjx/j4+KDVxs8WGxuLv78/AKGhofj4+HDjxo24OSLd3NzizjN37lxiY2Pp27cvdevWpVKlSgAMGzYMT09Pypcvj5ubG9u3b+fPP//kwIEDAKxcuRKdTkfVqlWxsrJizZo1WFpaki+fsTN//vz5OXr0KO3atcPc3Jzs2bOn2GeWpCs3ly5divf4+2/jOBM///yzDOInRGak0UBTL+Py5bXwLGWvluz8248W845z9qWeK0p+StRuytjv2mNhZrz0XThrYdY3X88Xhb5Ab9Az//J8eh/ozeuI1ymaS2QgJb4Az1VglzP+ertcxvUlPnyXUkqZNGkSer0+7rmiKPj4+DB37lx27dpFw4YNKVq0KN27d8fFxYXjx4/H2//69evkzJmTnDlzUq5cOXx9fVm4cCGdO3eOO97WrVvJmjUrderUwc3NjYIFC+Lj4xN3jJYtW/LLL78wa9YsSpYsyeLFi1mxYgX16tUDjJ2YlyxZQs2aNSlTpgwHDhxg+/btZMuWLe49PHr0iEKFCuHo6Jiin1eSZgVPz2RWcCFS0OY+cGUd5KoA3x40Fj3J6F1EFDOXb2T7M3PeGqyolC8r89qXJ1cWy4/us/XeVqacmUJEbATZLLIxvc50quWUGx8yumSbFVyvM/bBefcSbJyMfWzkCmCKUXVWcCGE+CC3CWBmCy8uGq/gJKPLd58wadZ8tK/vU8/sAX3qFmBDr2r/WdgAfOn6JRuab8A1iytvIt/Qa18vvC97o0uFYfRFBqDRQoHaULq18U8pbNKFBBc3TZo04fTp05/cLjQ0lBkzZuDt7f1ZwYQQ6ZCtE9QztuFzYAJEBCXLYVfuOMKmtb9jrX9HFCbUaeDGiKYlMNEm7EdYwSwFWdd8HV8X/hoDBhZdWcS3+74lIDwgWfIJIdKWBHcobtOmDV9//TX29vZ4eHhQqVIlcuXKhYWFBW/fvuXGjRscP36cXbt20bx5c7y8vFIytxAirarSGy78brw1/MhMaDI1yYcKCYvEa9kGTN4+xlSBMLOsfNelPQVzJb693tLEkgk1JlDZuTKTTk3i/MvztNnehqm1plIzd81PH0AIkW4kqs9NVFQUGzduxMfHh+PHj8dNuKUoCiVKlMDd3Z0ePXpQvHjxFAv8uaTPjRCp4N4BWPM1aEzguxPGwf4S6eojf1avXoO1PgyDAcxdSjG0S0tMTT6/WeBR8COGHhnK7bfGcUJ6lOpB//L9MdEk29BfQmXJ1udGpKrk6nPzWR2Kg4ODiYiIIFu2bKk+QE9SSXEjRCpZ38E4LUOButB5a6LG+th04RnjtlyltnITB20kNRo2xaNW+WSNF6WLwuucFz63jXeDlM9Rnpl1ZuJsnfJDw4uUJ8VN+qRqh+KoqCjCwsKwt7fH2dk53RQ2QohU5D4FtObw8Ajc2pGgXYLehfPDhosM3XiF8Bg9Ebkr0rt3r2QvbADMteaMqTaGWXVnYWNqw6WAS7Te3pqjz44m+7mEEKkrUcXNq1evaNq0adx06NWqVePevXsplU0IkZ45FIAaA4zLe3+EmIj/3PzMjYdMm+PNi+un0SjwQ6MirOhZh/zOKTfQF4B7fnd8W/hSIlsJgqOC6XewHz+f/5kYfUyKnlcIkXISVdyMGDGCy5cvM2nSJGbNmkVQUFCqTF0uhEinag8xDlMf9AROzPvgJnq9nsV/HmCH72qs9OHkNwlmZeeyDGhYGK3m84atTygXOxdWN11Nx+IdAVh5fSVdd3fl+bvnqXJ+IUTySlSfGxcXF5YuXYq7uzsAd+/epXjx4oSFhWFubp5iIZOT9LkRIpVd+wM2dQcTC+h/DrLkjXspMCSMWUvXYx5qLCLCLbIzoFsH8uTIqlZaDj45yNgTYwmNDsXWzJbJNSfTMG9D1fKIpJE+N+mTKn1uXrx4QdmyZeOeFy5cGHNzc/z8/BJzGCFEZlLyK8hXE2IjYd/YuNUnrt5jxlxvzEOfozeAdYFyTB3aR9XCBqBh3oZs9NhImexlCI0O5ftD3zPj7AyiddGq5hJCJFyiOxT/ezIurVZLJpvBQQiRGIoCTWeAooEbWzA8OMKqkw/Y9ocvVoYIIjCjZrOvGdr5S7QJHJQvpeW2yc3KJivpUsI4EfCam2votLsTT0OffmJPkdHo9DrO+Z9j14NdnPM/JyNbpxOJ+kliMBgoUqQIDg4OcY93795Rvnz5eOuEECIe59JQqTsALzYMYuK2a5yMzkeEZQ4G9utD4yqlVA74PlOtKUMrD2V+g/nYm9tz480NPLd7su/RPrWjiVRy4PEB3P9wp/ve7ow4NoLue7vj/oc7Bx4fSPFzP336lO7du5MrVy7MzMzIly8fgwYN4s2bN3Hb1KtXD0VRUBQFCwsLihQpwrRp0+JdcHj06FHcNv/7+Oabb1L8PagpUSNWrVixIqVyCCEyuBPZO2DHMUpH36azyQFyu39P95r50STz5JrJra5LXTZ5bGL40eFcCrjED0d+oK1/W4ZVHoa5Nn30NRSJd+DxAYYcHoKB+C0TAeEBDDk8hNn1ZuOWzy1Fzv3gwQOqV69OkSJFWL9+PQUKFOD69esMGzaM3bt3c/r06bgLCT179mTSpElERUXx119/0atXL7JkyUKfPn3iv58DByhZsmTcc0vL/56TLb2TWcGFEClKr9cz32cPr2+fR4OOwSzF2kyDyaBLYJ2yt3knpxh9DAsuL2Dp1aUAFHMoxqy6s8hnl0/lZOJDPtQx1WAwEBH730MSgLEpquW2lv8595iTlRObv9iMNgETaVqaWKIkYhDLpk2bcu3aNe7cuROvCPH396dQoUJ07tyZhQsXUq9ePcqVK8fcuXPjtqlYsSL58uXjzz//BIxXbgoUKMClS5coV65cgjOoJbk6FMtY40KIFPMyMIQ5y9ZhGf4SrQIRVs5oLV0xeX0Z/poMHr+oHTHBTDWmDKowiEpOlRh1bBS3Am/hud2T8dXH06xgM7XjiQSIiI2g6rqqyXKsl+EvqbGhRoK2PdPhDFamVgnaNjAwkL179zJlypT3rq44OzvTsWNHfHx8WLBgQbzXDAYDx48f59atWxQuXDhhbyIDS9vXg4UQ6dZfF24yZ/4CLMNfojMoZC1Smak/9MbGY5pxgwu/w4tL6oZMgpq5a7Lpi01UcqpEeGw4I46NYMLJCUTGRqodTWQAd+/exWAwfHSOxuLFi/P27VtevXoFwIIFC7CxscHc3Jw6deqg1+sZOHDge/vVqFEDGxubuMelS+nv/15iyJUbIUSy0uv1/LJ+F0F3L2KpGAhXLGj+ZSvqlCti3CBfDSjdBq5uhF3Doce+RM07lRbksMrBksZLWHRlEb/9/Rt/3P2DK6+u8HO9nyloX1DteOIjLE0sOdPhzCe3u/DyAn0P9v3kdgsaLqCiU8UEnTexPtVjxMzMDICOHTsyevRo3r59y/jx46lRowY1arx/RcnHxydeweTi4pLoTOmJFDdCiGTzNiyaoRuvEHH/MUVNDETa5GTot+1xtLeNv2GjSXBrFzw7C3/7Qtm26gT+DCYaE/qX709Fp4qMOjaKe0H3aLejHWOqjeGLQl+oHU98gKIoCWoeqpGrBk5WTgSEB7zXoRhAQcHJyokauWokqM9NYri6uqIoCjdv3qRVq1bvvX7z5k0cHR3JkiULAPb29ri6ugLg6+uLq6sr1apVw80tfmdnFxeXuO0yg2RpltLpdFy+fJm3b98mx+GEEOnQ+UdvaD7vGAdvBXDJkJ8cpWszZfC37xc2AHa5oM4PxuX94yAqNHXDJqPquaqz6YtNVM1ZlYjYCEYfH82Y42MIjwlXO5pIIq1Gy8gqIwFjIfO//nk+osqIZC9sALJly0ajRo1YsGABERHxOz/7+/uzdu1aunbt+sF9bWxsGDRoEEOHDs30488lqbj5/vvvWbZsGWAsbOrWrUuFChVwcXHh8OHDyZlPCJHG6XR6Zq3axuLlq3kRHEGB7NZs6luLPl81+O/bvKv3h6wF4J0/HPVKvcApILtldha7LaZfuX5oFA1b72+l/c723H17V+1oIonc8rkxu95scljliLfeycopRW8DB5g/fz5RUVG4u7tz9OhRnj59yp49e2jUqBFFihRh3LhxH923d+/e3Llzhz/++CPF8qUHSSpuNm3aFDcNw/bt23n48CG3bt1i8ODBjB49OlkDCiHSrmcBb/lx1kLCHl4ijzaY1oU0bB9Qi5K57D+9s4k5NJluXD61AF7fS9mwKUyr0fJd2e9Y2ngpjpaOPAh+QIedHfjz7p+Z/rfo9Motnxt7v97LcvflzKg9g+Xuy9nz9Z4ULWzAOLXRuXPnKFiwIJ6enuTLl4+mTZtSpEgRTpw4gY2NzUf3dXBwoHPnzkyYMAG9Xp+iOdOyJI1zY2Fhwb1798iTJw+9evXCysqKuXPn8vDhQ8qWLUtISEhKZE0WMs6NEMlj96m/ObpvJxZEE2tQyFOmBj1bfuJqzb8ZDLC2DdzbD4UbQ8eNKRc4Fb2JeMPo46M58eIEAM0LNmdstbFYm1qrnCzzyGgTZ44fP57Zs2ezf/9+qlWrpnacFKPKxJn/cHJy4saNG+h0urhLZQDh4eHvzT0lhMhYYmJ1zFyxmdN7N2NBNOEaK1p4dqL3V26JH21YUaDJNNCYwt19cGdvyoROZdkss7HAbQGDKgxCq2jZ+WAn7Xa043bgbbWjiXRq4sSJzJs3j9OnT2fqKzIJlaTiplu3bnh6elKqVCkURYnrlX3mzBmKFSuWrAGFEGnHq9Aohs9ZScSTv9EoEG2fl1GD+1G1RIGkHzR7Yaj2/4eK3zMSYqOSJ6zKNIqGb0t/y4omK3CycuJRyCM67OyA721faaYSSdKtWze+//77ND9lSVqQpE9owoQJLF26lF69enHixAnMzY3zq2i1WkaOHJno43l7e5M/f34sLCyoWrUqZ8+eTdB+GzZsQFEUWrZsmehzCiES58S91zT95RhHAu2IMpiQu1wdpnzfjSw2CRt59T/VHQ42ThD4AE55f/7x0pDyOcqzyWMTdfPUJVofzeTTkxl2dBjvot+pHU2IDEv1uaV8fHzo3LkzixYtomrVqsydO5eNGzdy+/ZtcuTI8dH9Hj16RK1atShYsCAODg5s2bIlQeeTPjdCJE5MrI65286w4NxbDAYo6mTLXM+SFM+dLXlPdGUDbO4NptYw4LzxdvEMxGAwsOrGKuZemEusIRYXWxe86npRMlvJT+8sEi2j9bnJLFTtcwNw8OBBWrRoQaFChShUqBAtWrTgwIHETwM/e/ZsevbsSbdu3ShRogSLFi3CysqK5cuXf3QfnU5Hx44dmThxIgULymigQqSUBy9eMdrLm9C/D5CNMNpVdmFLv5rJX9gAlPaEPFUgJgz2j0/+46tMURS6lOzCyqYryWWdi6ehT+m0qxNrb66VZqoUJP1T0pfk+r+QpBGKFyxYwKBBg2jdujWDBg0C4PTp0zRr1ow5c+bQr1+/BB0nOjqaCxcuMGrUqLh1Go0GNzc3Tp069dH9Jk2aRI4cOejRowfHjh1LylsQQnzC5iMXOHd4D9bEEoOGgXXy0LlpmZQ7oUYDzWbCb/Xhqi9U6g75qqfc+VRS1rEsvh6+jDsxjr+e/sX0s9M553+OiTUmYm+egFvoRYKYmZmh0Wh48eIFjo6OmJmZJWpmbpH6DAYDr169QlEUTE1NP+tYSSpupk6dypw5c+jfv3/cuoEDB1KzZk2mTp2a4OLm9evX6HQ6nJyc4q13cnLi1q1bH9zn+PHjLFu2jMuXLyfoHFFRUURF/V8HxbR8m7oQaUF0TCxeK/4k1u8m5kCYxoYO7dpQrnDelD95rvJQoTNc/B12D4NeRyAFRoFVm725PXPrz2XdrXXMOj+Lg08OcivwFl51vCjtWFrteBmCRqOhQIEC+Pn58eLFC7XjiARSFIU8efJ89p3XSSpugoKCaNKkyXvrGzduzIgRIz4r0H8JDQ2lU6dOLFmyhOzZsydon2nTpjFx4sQUyyRERnL32UuWrNqAdUwQALpsBRnXwxMbS/PUC9FwHFzfAv5XjUVOpe6pd+5UpCgKHYt3pJxjOYYeGcqzd8/ovLsz31f8ns4lOstVhmRgZmZG3rx5iY2NRafTqR1HJICpqWmyDCmTpA7FHTp0oHz58gwbNize+lmzZnH+/Hk2bNiQoONER0djZWXFpk2b4t3x1KVLF4KCgti6dWu87S9fvkz58uXjvfF/2lM1Gg23b9+mUKFC8fb50JUbFxcX6VAsxL/8desl3j57KMMjYgxailatR6emtdQJc3oR7BkBlg4w4AJYOaiTI5WERocy/uR49j/eD0C9PPX4qdZP0kwlxP9ITIfiBF+5mTdvXtxyiRIlmDJlCocPH6Z6dWOb+OnTpzlx4gQ//PBDgoOamZlRsWJFDh48GFfc6PV6Dh48GK/J6x/FihXj6tWr8daNGTOG0NBQfvnllw9O4W5ubh53q7oQ4n0xOj1ee2/z29EHQDYcsxro16YRpQvmVi9U5R5wYSW8ugmHpkLzWeplSQW2Zrb8XPdnfG/7MvPcTA4/O0zr7a3xquNFuRzl1I4nRLqT4Cs3BQokbJAuRVF48OBBggP4+PjQpUsXFi9eTJUqVZg7dy6+vr7cunULJycnOnfuTO7cuZk2bdoH9+/atStBQUFyK7gQSXDzsR+L1m9jZ1BOYtHStUZ+RjUrhrlJGujn8uAIrPoCFA30PgbOpdROlCpuBd5i6JGhPA55jFbRMqD8ALqV6oZGkYHbROaWIlduHj58+NnBPqRt27a8evWKcePG4e/vT7ly5dizZ09cJ+MnT57IaIxCpID1+09z7cQBHBQdNSwMdGzTEveSzmrH+j8F60KJL+HGVtg9ArruME7XkMEVcyiGTwsfJp2axK6Hu5h7cS7nX55nSq0pOFhk7OY5IZLLZw3i9/r1a4AEd+5NC+TKjcjsIqJimLnMF14ZZ+EOM7Gja8d2lMifU+VkHxD0BOZXhthIaL0CSn2ldqJUYzAY+PPun0w7O40oXRQ5LHMwo84MKjlXUjuaEKpI0UH8goKC6NevH9mzZ8fJyQknJyeyZ89O//79CQoKSmpmIUQquP7gBeO9fo0rbBSnIkwc2j9tFjYAWfJCrcHG5X1jIDpM3TypSFEUvi7yNeuar6OAfQECIgLosa8Hi68sRqeXO3+E+C+JunITGBhI9erVef78OR07dqR48eIA3Lhxg3Xr1uHi4sLJkyfJmjVrigX+XHLlRmRWG/66wLWjuzFVdERjQrlajWjTsIrasT4tJgLmV4HgJ1BnGDQYo3aiVBceE86UM1PYdn8bANVyVmNa7Wlkt0w/V82F+FyJ+f5OVHHz/fffc/DgQQ4cOPDewHv+/v40btyYhg0bMmfOnKQlTwVS3IjMJjJGx5SdN9l0+i5fWtxAb2bDt53aUcTF6dM7pxU3toFvJ9CaQ78z4PAZs5CnY1vvbWXKmSlExEaQ3TI702tPp2rOqmrHEiJVpFiz1JYtW5g1a9Z7hQ2As7MzM2fOZPPmzYlLK4RIMTefvuKrBSdZffoxEZhhX7YRk4b2TV+FDUBxDyhQF3RRxuapTOpL1y9Z33w9rllceR3xmp77euJ92VuaqYT4l0QVN35+fpQs+fEZbEuVKoW/v/9nhxJCfL6VO46ydtkiwl4+wsHajJXdKjOqVRUszD5vzhZVKAo0nQGKFm7tgHsH1U6kmkJZCrGu+Tq+Lvw1BgwsurKInvt7EhAeoHY0IdKMRBU32bNn59GjRx99/eHDhzg4yK2KQqgpNDySsfN+5/GFQ5gqeirbhbJ7UG3qFc2hdrTPk6M4VO1tXN49AmKj1c2jIksTSybUmMD02tOxMrHinP852mxvw8nnJ9WOJkSakKjixt3dndGjRxMd/f4PlaioKMaOHfvBOaeEEKnjwu3HTP55PiZvH2EwgFnukkz9oRdOdhZqR0sedUeAVXZ4cxfO/qZ2GtU1L9gcnxY+FM1alMDIQHof6M0vF38hVh+rdjQhVJWoDsXPnj2jUqVKmJub069fP4oVK4bBYODmzZssWLCAqKgozp8//8FpENIK6VAsMqqlWw/x+NJxTBQ9kZhSvUFTvqhdXu1Yye/iKtg2AMxsjfNO2aaz/kMpIEoXhdc5L3xu+wBQIUcFZtSZgbN1GhqUUYjPlGJ3S4Gx6alv377s27ePf3ZVFIVGjRoxf/58XF1dk548FUhxIzKa8OhYJmw4gfXDw8bn5g706dqe/M4Z9DZhvR6WNoAXl6BcR2i5QO1EacaeR3uYcHICYTFhZDHPwpRaU6iTp47asYRIFila3Pzj7du33L17FwBXV9d009dGihuRkdx5GUq/tRe5G/COKqZPqVgwB0M6fYlpWpgbKiU9PQfL3IzL3x6EPDJq7z+ehjxl6NGh3HhzA4BuJbsxoMIATDXpsCO5EP8jVYqb9EqKG5ER6PV6lm49xMKL73gbY0IOW3PmtS9PtYLZ1I6Wejb3gSvrIFcFY4Ejc9DFidZF8/P5n1l3ax0AZRzL4FXHi1w2uVROJkTSpej0C0IIdQWGhDFm7nL8/j5ONeUedQpnY9eg2pmrsAFwG2/sd/PiorHIEXHMtGaMqjqKufXmYmtmy9+v/qbN9jb89eQvtaMJkSqkuBEiHTl59T4z5npjHvocvQEKubqyomsVstuYqx0t9dk6Q93hxuUDEyAyWNU4aVHDfA3Z6LGR0tlLExIdwqBDg5hxdgYxuhi1owmRoqS4ESId0Ov1LNi4jz1/rMXKEEEEZtRo9jVDO3+JVpuJ/xtX/Q6yFYawV3B4htpp0qTcNrn5vcnvdCnRBYA1N9fQaXcnnoY+VTmZECknE/9UFCJ9eBP8jtFzlvHqxim0ioFwyxwM7NcH9yql1I6mPhMzaDLduHx2MQTcUjdPGmWqNWVo5aH82uBX7M3tuf7mOp7bPdn3aJ/a0YRIEVLcCJGGXXseTOvFp4kIeYveoGBXqCJTf+hNruxZ1I6WdhR2g6LNQB8Le0ZA5rpHIlHqudRjY4uNlHMsx7uYd/xw5Ad+Ov0TUbootaMJkaykuBEiDdLr9aw88ZCvFpzkYWAUN8xLUKdFawZ/0yJzN0N9jPtU44zhDw4b554SH5XTJifLmyynR6keAPjc9qHTrk48DnmscjIhko/8lBQijXn5NpQff17CH7sPEq3T07iEExu/d6dhpRJqR0u7HApAjQHG5b0/QkyEunnSOFONKd9X/J6FbgvJap6Vm4E38dzuye6Hu9WOJkSykOJGiDTk0MVbzPnVG8twf8qZ+DGmcQEWd6qIvZUMwPZJtYeAbS4IegInf1U7TbpQK3ctNnpspKJTRcJjwxl+dDgTT00kMjZS7WhCfBYpboRIA/R6PXPX7uDQNl8sDVGEKxa4fenJtw1KoCiK2vHSBzNraDzZuHxsNgTJ3UAJ4WTtxNLGS+lVphcKCpvubKLDrg48CH6gdjQhkkyKGyFU5v8mmB9nLSb43gW0ioFI65wMHdSXuuWLqh0t/Sn1NeSrCbERsG+M2mnSDRONCQPKD2Bxo8Vks8jG3bd3abejHdvvb1c7mhBJIsWNECo69+AVs+cvwjIiAJ1BIXvxakwZ8i2O9rZqR0ufFAWazgBFAze2wMOjaidKV6rnqs6mLzZR1bkqEbER/Hj8R8aeGEt4TLja0YRIFCluhFCBXm9g0ZH7tFt6jr+jcxCuWOL+dXv6ebqjkTmSPo9zaajU3bi8ewToYtXNk85kt8zO4kaL6VuuLxpFw5Z7W+iwswP33t5TO5oQCSY/RYVIZc8C3tJ76V9M330Lnd5AkZJlGTlkADVLF1Y7WsZRfzRYZoWAG3B+udpp0h2tRkufsn1Y2ngpjpaO3A++T/ud7dl8dzOZbK5lkU5JcSNEKtp96ireCxdh9/wcNiZ6pn9Vml/alyerjaXa0TIWKwdoMNa4fOgnCHutbp50qrJzZTZ6bKRGrhpE6iIZd3Ico46PkmYqkeZJcSNEKtDp9MxcuZnTe//EgmhMtFpWdCpLuyp55W6olFKxKziVNk6o+ddktdOkW9kss7HQbSGDKgxCq2jZ+WAnbXe05XbgbbWjCfFRUtwIkcIe+wfyo9cCIh7/jUaBaHsXRg3pR+WiedWOlrFptNBspnH5wu/w4rKqcdIzjaLh29Lfstx9OU5WTjwKeUSHnR3wve0rzVQiTZLiRogUtOP4ZRYtXoRV1BtiDRpyl63DlO+7k8XGSu1omUO+GlCqNWCA3cNl3qnPVMGpAhs9NlInTx2i9dFMPj2Z4UeH8y76ndrRhIhHihshUoBOb2DO/jts2XcYC2II01jxZdtOfNuyvtrRMp/Gk8HUGp6egb991U6T7mW1yMqvDX7lh4o/YKKYsOfRHjx3eHLjzQ21owkRR4obIZJZQEgkHZee5peDdzkWnZ+Y7K6MGTKASsXzqx0tc7LLBXV+MC7vHwdRoermyQA0ioaupbqysulKclnn4mnoU77Z9Q3rbq6TZiqRJkhxI0Qy2nLkAj/MWc3pB4FYm2mZ2a4yP/XriJ21hdrRMrdq/SBrAXjnD0dnqZ0mwyjrWBZfD1/qu9QnRh/DtLPTGHJ4CCHRIWpHE5mcFDdCJIPomFimLPHl8qEduPKCGtki2T6gFl+Wy612NAFgagFNphmXT3nDaxmQLrnYm9vzS/1fGFF5BCYaEw48OYDndk+uvrqqdjSRiUlxI8RnuvssgDFe3sS+uImigM6hAIv7Naego43a0cT/KtIEXBuBPgb2jlI7TYaiKArflPiG1U1Xk9smN8/fPafzns6sur5KmqmEKqS4EeIz/PHXOVYsW4J1TBAxBi0FKjdg0oDO2FpJM1SaoyjGqzcaU7i7D+7sVTtRhlMqeyk2emykUb5GxOpj8TrvxcC/BhIcFax2NJHJSHEjRBLE6PT8tPQPrh3bhTmxhGltadu5G52b1VY7mvgv2QtDtT7G5T0jITZK3TwZkK2ZLT/X/ZkxVcdgpjHj8LPDtN7emssBl9WOJjIRKW6ESKRnb8PxXHyKXQ+iMBjA4OjK+KH9KF1Q+tekC3WGgY0TBD4w9r8RyU5RFNoWa8va5mvJZ5cP/zB/uu7pyvJry9Eb9GrHE5mAFDdCJMLOCw9pPu84l54E8c4sG2UbezKhb0esLczVjiYSysIOGk0yLh+dBSEv1M2TgRVzKIZPCx+aFmiKzqBjzoU59DvYj8DIQLWjiQxOihshEiAiKoaJC9dxYvtadJHvKOuShZ0Da9OqRnG1o4mkKO0JeapATBjsH692mgzN2tSaGbVnML76eMy15hx/fpw229pw4eUFtaOJDEyKGyE+4cbDF4z3mg8BdzFXdLQtYsLG3tVxcZApFNItjQaazgAUuOoLT06rnShDUxSF1kVas675OgrYFyAgIoDue7vz29+/STOVSBFS3AjxH9buOcHa35djrQshGhOK12zC2G5fYGYi/3XSvdwVoEIn4/KuoaDXqZsnEyiStQgbmm/gi0JfoDfo+fXSr3y3/zteR7xWO5rIYOQntBAf8C4iivHeq7l35gBmio4wU3s6d/sWT7eqakcTyanheDC3B/+rcPF3tdNkClamVkypNYXJNSdjaWLJKb9TtNnehjN+Z9SOJjIQKW6E+JdHr8MY9OtGNK8fAKDNWYxJQ/tRNK+TyslEsrPODvV/NC4fnAzh0tE1tbR0bcn65utxzeLK64jX9NzXkwWXF6CTK2giGUhxI8T/2HblBS1+Pc6hQHtekoVSdZoxpldbLMxM1Y4mUkrlHuBYHCIC4fA0tdNkKoWyFGJd83V8VfgrDBhYeGUhvfb34lX4K7WjiXROMWSysbFDQkKwt7cnODgYOzs7teOINCI0PJIZq3ey9qE5BhSqFHBgXrvyONvLSMOZwoMjsOoLUDTQ+xg4l1I7Uaaz48EOJp2aRERsBA4WDkyrNY0auWuoHUukIYn5/pYrNyLTu3D7MZN/no+p/zXKm75gQANX1n1bVQqbzKRgXSjxJRj0sHsEZK7f+dKEFgVb4NPChyJZixAYGch3B75j3sV5xOpj1Y4m0iEpbkSmtmzrITavX4W1PoxITOnauDI/NC6KiVb+a2Q6jX8CEwt4fByub1Y7TaZUwL4Aa5utxbOIJwYMLLm6hB57e+Af5q92NJHOyE9wkSkFvQtnzC8reHb5KKaKnjAzB3r37sUXtcurHU2oJUteqDXYuLxvLESHqZsnk7IwsWBs9bF41fXC2tSaiwEXabO9DUefHVU7mkhHpLgRmc65W4+YOscb06An6A1g4VKaKcP6kt85u9rRhNpqDgL7vBDyDI7PUTtNptYkfxN8W/hS3KE4QVFB9DvYj9nnZxOjj1E7mkgHpLgRmYbBYMD3/FP6rb2AuS6SSEyp0qglI7p/hamJVu14Ii0wtQT3KcblE/Mg8KG6eTK5vHZ5WdNsDR2KdQBgxfUVdNvTDb93fionE2mdFDciUwiNiGaI7xWGb/qbgBgLAhzL06fPdzSvWVbtaCKtKe4BBeqCLgr2jVE7TaZnpjVjVNVRzKk3B1tTW668ukLr7a059OSQ2tFEGibFjcjwTl27z+RZv3Diyi20GoVh7kVZ2Kc5eXM4qB1NpEWKYpx3StHCrR1w76DaiQTgls8NXw9fSmUrRUh0CAMPDWTG2RnE6KSZSrxPihuRYen1ehZu3MfuTWux1odTw+IF63tWpV99VzQaRe14Ii3LURyq9DIu7xkJsdHq5hEA5LHNw6qmq+hcojMAa26uofPuzjwLfaZyMpHWSHEjMqTXwe8YM3cZATdOoVUMhFs6MqxPd6oUyKZ2NJFe1BsJVtnh9R04+5vaacT/Z6o1ZVjlYfza4FfszOy49uYants92f94v9rRRBoixY3IcI5duYvXL96Yh75Ab1CwLVSBqT98R27HLGpHE+mJZRZwG29cPjwdQl+qGkfEV8+lHps8NlHWsSyhMaEMOTyEKaenEKWLUjuaSAOkuBEZhsFgYMm+S+zfvB4rQyQRmFO7RWuGfOOBVgblE0lR7hvIVR6iQ+HgJLXTiH/JaZOTFU1W0L1UdwA23N5Ap12deBLyROVkQm3yE19kCMERMfRde5Epfz3nqd6eCCsnBvfvi1ulEmpHE+mZRgNNvYzLl9fAs/Pq5hHvMdWYMrjiYBY0XEBW86zcDLyJ5w5Pdj/crXY0oaI0Udx4e3uTP39+LCwsqFq1KmfPnv3otkuWLKF27dpkzZqVrFmz4ubm9p/bi4zv8KXbfDnvELuv+WOq1VCzYTOm/tALp2wyMapIBi6VoaxxnBV2DQO9Xt084oNq56nNRo+NVMhRgbCYMIYfHc7EUxOJjI1UO5pQgerFjY+PD0OGDGH8+PFcvHiRsmXL4u7uTkBAwAe3P3z4MO3bt+fQoUOcOnUKFxcXGjduzPPnz1M5uVCbXq9n7rqd/LXVh/zvbuGS1YJN39Xg27qF0WhU/6ctMhK38WBmCy8uwpV1aqcRH+Fk7cQy92X0KtMLBYVNdzbRcVdHHgbLYIyZjWIwqDv9bdWqValcuTLz588HjF9YLi4uDBgwgJEjR35yf51OR9asWZk/fz6dO3f+5PaJmTJdpF3+b4KZu2w9lhHGTp6R1jkZ9l0nHGwsVU4mMqwT82D/WLB2hAEXwMJe7UTiP5x8cZJRx0YRGBmIpYklY6uNxaOQh9qxxGdIzPe3qr/eRkdHc+HCBdzc3OLWaTQa3NzcOHXqVIKOER4eTkxMDA4OMiBbZnHg3A3mzl+IZcRLdAaFbMWqMmXIt1LYiJRV9TvIVhjCXsHhGWqnEZ9QI1cNNnlsoopzFSJiI/jx+I+MPTGWiNgItaOJVKBqcfP69Wt0Oh1OTk7x1js5OeHvn7Ap7keMGEGuXLniFUj/KyoqipCQkHgPkT7pdHp+Xr2NYzs3YkkU4YoFjVu1p3/bJtIMJVKeiRk0mW5cPrsYAm6pm0d8kqOVI781+o2+5fqiUTRsubeF9jvac+/tPbWjiRSWrr8Rpk+fzoYNG9i8eTMWFhYf3GbatGnY29vHPVxcXFI5pUgOgWHR9Fpxkpf3r6FRIMo2NyO+70+tsoXVjiYyk8JuULQZ6GNhzwhQt1VfJIBWo6VP2T4sbbyU7JbZuR98n/Y727P57mZU7pUhUpCqxU327NnRarW8fBl/cKyXL1/i7Oz8n/vOmjWL6dOns2/fPsqUKfPR7UaNGkVwcHDc4+nTp8mSXaSesw8DafbLMQ7eC+akrhA5StXgp++742BnrXY0kRm5TwGtGTw4DLd2qp1GJFBl58ps8thEjVw1iNRFMu7kOEYfH014TLja0UQKULW4MTMzo2LFihw8+H8T0+n1eg4ePEj16tU/ut/MmTOZPHkye/bsoVKlSv95DnNzc+zs7OI9RPqg0+nx+n0LPy7bgX9IJAUdrVnStyl9vm4kzVBCPQ4FocYA4/LeURAjfTjSi2yW2VjotpCB5QeiUTRsf7CdtjvacjvwttrRRDJT/RtiyJAhLFmyhN9//52bN2/Sp08fwsLC6NatGwCdO3dm1KhRcdvPmDGDsWPHsnz5cvLnz4+/vz/+/v68e/dOrbcgUsBj/0B+9FpA+KMrVDN5zNelHNjevxbFc0pxKtKA2j+AbS4IegInf1U7jUgEjaKhZ5meLHdfTg6rHDwKeUSHnR3YeGejNFNlIKoXN23btmXWrFmMGzeOcuXKcfnyZfbs2RPXyfjJkyf4+fnFbb9w4UKio6Np3bo1OXPmjHvMmjVLrbcgktmOE5dZtHgRVlFviDVoyFe2BrM6VsPa3ETtaEIYmVlD48nG5WOzIUiau9Obik4V2eSxidq5axOtj2bSqUmMODqCd9Hyi3JGoPo4N6lNxrlJu2Jidfy8agsRT4ydhsM0Vni2bkOl4vnVjibE+wwGWNEMnpyEkq2gzUq1E4kk0Bv0rLq+il8u/kKsIZa8tnmZVXcWxbMVVzua+Jd0M86NEP/wDwpntNcCop4aC5vYLPkYM2SAFDYi7VIUaDYTFA1c3wwPj6qdSCSBRtHQtVRXVjRZQU7rnDwJfULHXR1Zf2u9NFOlY1LcCNUdu/uKFvNPcD/MjBiDhrwV6jF5UFfsrD98e78QaYZzaahknJGa3SNAF6tuHpFk5XKUY6PHRuq51CNGH8PUM1P54cgPhETL2GjpkRQ3QjXRMbF47bhC5+Vnef0umvBsRWn9TTe6edRVO5oQCVd/NFhmhYAbcH652mnEZ7A3t2de/XmMqDwCE40J+x/vx3O7J9deX1M7mkgkKW6EKu4+C2CMlzePz+1DMejpWDUvm/vXppxrHrWjCZE4Vg7QYIxx+dBPEPZG3TzisyiKwjclvmF109XktsnN83fP6bS7E6tvrJZmqnREihuR6v44dI4Vy5ZgHRNEFiWKaU3zMaVVaSxMtWpHEyJpKnYDp9IQGQx/TVI7jUgGpbKXwtfDl0b5GhGrj2XmuZkMPDSQ4KhgtaOJBJDiRqSayOgYJi/ewLWjuzAnljCtLZ7fdKVt3Y+PMC1EuqDRGjsXA1z4HV5cVjWOSB52Znb8XPdnfqz6I6YaUw4/PUyb7W24HHBZ7WjiE6S4Eani1hN/xnl5o/c3jgRqcHRl/NB+lJFmKJFR5KsBpVoDBtg9XOadyiAURaF9sfasbbaWvLZ58Qvzo9uebqy4tgK9Qa92PPERUtyIFLf/xkvmrdiAdWww0QYthas1YkLfjlhbmKsdTYjk1WgSmFrB0zNwdaPaaUQyKp6tOD4tfGiavymxhlhmX5hN/4P9eRv5Vu1o4gOkuBEpJjpWz+QdN+i56jzHIl0IMXWgY5fudHCvoXY0IVKGfW6oM9S4vG8sRIWqm0ckKxszG2bUmcH46uMx15pz7PkxWm9vzYWXF9SOJv5FihuRIm48fMF3v2xi2fGHALStWZxpw/tRokAulZMJkcKq9YOsBeCdPxyVaWEyGkVRaF2kNWubrSW/XX4CwgPosbcHS/5eIs1UaYgUNyLZrdt7grW/Lydv6E1cLcNY2rkSY1qUwMxE/rmJTMDUAppMMy6f8oY399XNI1JEUYei+LTwwaOgBzqDjnmX5vHd/u94EyFDAaQF8m0jkk1YZBQTvFdz9/QBzBQd4ab2zO9WB7cSTmpHEyJ1FWkCrm6gj4E9o9ROI1KIlakVU2pNYVKNSVhoLTjld4rW21tz1u+s2tEyPSluRLL4+94zJs6aj/L6AQCanEWZNKwfxfI6q5xMCBUoCjSZDhpTuLsX7uxVO5FIIYqi0KpwKza02EAh+0K8jnhNz/09WXh5ITq9Tu14mZYUN+Kzrdp1HN81K7HWvSMKE0rVacbYXu2wMDNVO5oQ6sleGKr1MS7vGQmxUermESmqUJZCrG+xnlaurdAb9Cy4soDe+3vzKvyV2tEyJSluRJJFxuj4cfNVVp24j6miI8w0C9179OTr+pXVjiZE2lBnGNg4QeADOL1A7TQihVmaWDKp5iSm1pqKpYklZ/zP0Hp7a06+OKl2tExHihuRJHdfhtDS+wTrzjzhvj4bFkVq8NOwfrjmyaF2NCHSDgs7cJtoXD7iBSEv1M0jUoVHIQ82tNhAkaxFCIwM5Lv93zHv4jxi9TJrfGqR4kYk2vJth1i4cCEP/d+S3caMVd2rMqJ9I8xMTdSOJkTaU6Yt5KkCMWGwf7zaaUQqKWhfkLXN1tKmSBsMGFhydQk99vbAP8xf7WiZghQ3IsGC30Uw5pcVPL10FHslksaOoewaWJvahR3VjiZE2qXRQNMZgAJXfeHJabUTiVRiYWLBuOrj8KrjhbWpNRcDLtJmexuOPTumdrQMT4obkSDnbj5k6pz5mAY9wWAAC5fSzB7Unhx2FmpHEyLty10BKnQyLu8aBnIXTabSpEATfFv4UtyhOEFRQfQ92JfZF2YTo49RO1qGJcWN+E8Gg4Elmw+yzWcNVvpwIjGlcqOWjOj+FaYmWrXjCZF+NBgH5vbg/zdc/F3tNCKV5bXLy5pma/h/7d15WFTl38fx9wwwww4qsiloLqXmigs/XHIJxSzMyrQ0UzMrNbXo0TQ1TNPcMn+paWlGpYVi6WNqbiQl6s9yTVMpBZd+Ai4kIMo2cz9/8DRFYoHCHGb4vq6L6zpz5j5zPnMDc75zn+3JRk8C8NGxjxi6ZSip11I1TmafpLgRt5STV8jE99dx4cdEHHVmrhtrMOKFF3iwQwutowlhe9xrQtfXiqbjp8P1DG3zCKszOBh4LfQ15neZj4eTB0cuHaHvV33ZeW6n1tHsjhQ3okQnUrPovSiRL884kGU24lq3BTPHjSTYr7rW0YSwXW2HQc3GcCMDEt7SOo3QSPc63VkTuYamNZqSlZ/FmJ1jmPPDHApMspuqvOiUUkrrENaUlZWFl5cXmZmZeHp6ah2n0jGbzXz49T7m7f2NvEKFv6cz7/RrRlgDOcVbiHKRnACfPAw6PbyQCH73ap1IaKTAVMA7B9/h0+OfAtDMpxlz7ptDbY/aGiernMqy/ZaRG2FxOfMakxes4ML+bdRRF+l6T002j+0khY0Q5aleF2jcG5QZNo+HqvX9UvyJk4MT49uO592u7+Jp8OTo5aP0+6ofO87u0DqazZPiRgCQeOQX5v57Mcbs/2JWOno2qcmHg9tS3c2gdTQh7E/EDHB0hrOJ8NM6rdMIjXUN7kpcZBwtarYguyCblxNeZua+meSb8rWOZrOkuKnizGYzi1ZvYdu6z3FVudzASKcH+xI18EH0ep3W8YSwT97B0PHloultUyA/R9s8QnOB7oF81PMjhjYdCsDnJz/nqc1PcS7rnMbJbJMUN1XYxavZTJq/jCsn9+GgU9xw8eOlF0cQ3raJ1tGEsH8dxoJXMGT9CokLtE4jKgEnvRNRraNYfP9ivI3enMg4Qb+N/diSskXraDZHipsq6sj5qwxbsgPDtTRMSod3wzbM/J/n8K/hpXU0IaoGJxeIeLNoeve/ISNF2zyi0riv9n3ERcYR4htCTkEO474bx7S908gtzNU6ms2Q4qaKUUqxIjGFvkv3cDTTQJKhPt1692PsgAfR6+XPQQiratwb7uoMpjzYNlnrNKIS8Xfz58OIDxnebDg6dMT9HMfAzQNJyZQiuDTkVPAqJO1KJu98tIavr1QnSznzQFN/Zj3WHC8XJ62jCVF1XTwBSzqAMsFTX0KD+7VOJCqZPRf2MHHXRDJyM3BxdGHKv6YQWT9S61hWJ6eCi5vs2H+cBYuW4Jpzgc6GFN7o3YT3BoZIYSOE1nwbQ7vniqa3TAC5kJv4i/aB7VkbuZZ2/u24UXiD1xJf4/Xdr3Oj8IbW0SotKW7snMlkZv7KDezauBYX8riuc6bfow8zuP1d6HRyNpQQlUKXCeDqA5d/hn3va51GVEI1XWvyQfcPGNliJDp0rDu1jgGbBnD66mmto1VKUtzYsf9eusprby8l+/Qh9DpFnkcg48aOolOLhlpHE0L8mYs3hEcXTSfMgux0TeOIyslB78CIliNY3mM5Pi4+nLp6iic2PsH6U+u1jlbpSHFjpxJ+TGbRe0twvXGJQqXD9972vPnSMHy83LWOJoQoScunILAV5GdD/DSt04hKrF1AO+Ii4wgLCCPXlMuU3VOYlDiJ6wXXtY5WaUhxY2fMZsXinad4NvY4mSYnrutc6NV3ICP6dpezoYSozPR6eGBO0fThlfDrAW3ziErNx8WHpd2XMqbVGPQ6PRtOb+CJTU/w828/ax2tUpCtnR05f/E3Bq/4D3O3JlFo1uFyT0cmvPwiYU3rax1NCFEaQe2gxZNF05v/B8xmbfOISk2v0zO8+XBWRKzA19WXlMwUBmwawNqf11LFToS+iRQ3dmLT7iO8t2QJuWeO4OykZ07f5swbEEo1D1etowkhyiJ8Khg84MJBOPKZ1mmEDWjt15q1kWvpWKsjeaY83tj7Bq9+9yrX8q9pHU0zUtzYuIJCE7NXfMn329fjTAF3GbNZ93wo/doEydlQQtgiD3/oPL5oesdUyM3UNI6wDdWcq7H4/sVEtY7CQefA12e+pv/G/py4ckLraJqQ4saGpaReZtLc98g9fxS9Dgq8g5kcNZrGtatrHU0IcSdCX4AaDSDnEnw7R+s0wkbodXqGNh1KTM8YAtwCOJd9joGbBxJ7MrbK7aaS4sZG/e93h1j2wQe45WdQoPQEtbqPN8cOxdPNWetoQog75WiAnrOLpvcthUtJ2uYRNqWlb0viIuPoEtSFAnMBM/bN4JVvXyE7P1vraFYjxY2NKTSZmbf5KN9/swkjBeTo3Xjkyad5pndXraMJIcpTw3C4pxeYC+Hr8VDFvnmLO+Nl9OLdru8yvu14HPWObD+7nce/epxjl49pHc0qpLixIWmZuQxYvo9F351jT0EdTNXrMuWVF2l9Tx2towkhKkLEDHAwQHICnNykdRphY3Q6HYOaDOLTBz6llnst/nvtvwz6ehArj6+0+91UUtzYiC92/sDT/97I9ykZuBsdebnf/UwbPRgPV9kNJYTdql4P2o8umt46EQrkXkKi7Jr6NGVN5BrCg8MpNBcy+4fZjN05lsw8+z1YXYqbSi43v4A3P1jNse8208qcRAt/ZzaO7kjvFoFaRxNCWEPHKPAIhKvnYM9CrdMIG+Vp8GR+l/m8FvoaTnondp7fyeNfPc6RS0e0jlYhpLipxJLOpfP63MWYUk8C4FIzmFXPd6Suj5vGyYQQVmN0hx7Ti6Z3zYer57XNI2yWTqfjyUZPsrLXSoI8gkjNSWXI10OIORaDWdnXBSOluKmkVu/YxycfLcOtMJN85UCD0HCmjhqEu4tR62hCCGtr+hgEt4fCG7B9itZphI1rUqMJax5aQ8+6PSlUhbx94G1GfzOa33J/0zpaudEpez+q6C+ysrLw8vIiMzMTT09PrePcJDe/kDkfrkFd/AWAHAdPBg/sz713yW4oIaq01B/hg86gzDB4I9zVSetEwsYppVj7y1pm7ZtFvjkfX1df5t43lxC/EK2jlags228ZualEzmdcp/8H/+HUhStFM3wb8sa4F6WwEUJAQHNoPbRo+utXwVSobR5h83Q6HY/f/TifPfgZdT3rcvH6RZ7Z+gzLjy63+d1UUtxUEl//eIFe7+7iyK+Z/OTYgMYdexE9YgAuRietowkhKotuk8GlGlz8Cfav0DqNsBP3VL+H1Q+t5qF6D2FSJv598N+M2DGCKzeuaB3ttklxo7Gc3DymLl7J2rVxZOcWEBLszf+O6Uy/+9tqHU0IUdm4Vi8qcAB2vgk5trvxEZWLq5MrMzvOZFr7aTg7OLPnwh4e/+pxfkj7Qetot0WKGw0dPf0rb8xbjO7yaYIdrjI8xJPVz4dRu5rcyVsIcQuth4Jfs6Iban4zXes0wo7odDoeafgInz/4OfW96nPpxiWe3fYsS44swWQ2aR2vTKS40cgnm3ex+tMY3EzZ5OHIvZ16ManffTg5yK9ECPE39A7wwP/fd+pADFw4rGUaYYcaVGvAZw9+Rp8GfTArM+8dfo/ntz/P5RuXtY5WarIltbLs67m8vvATUn74BiediRwnb4YOG07fbrIbSghRSnU7QNO+gJL7TokK4erkyvQO05nZcSYuji7sS9vHYxseY++FvVpHKxUpbqwo+dI1Jr/zIQ4ZKSgFToFNeHPcKBrW9tU6mhDC1nSfBk6ucH4fHI3TOo2wU5H1I4l9KJaG1RqSkZvB89ufZ+GhhRSaK/fZelLcWMn6Q//loYWJ7L7mww0MtOz2EK8NfxyDk6PW0YQQtsirFnR6pWh62xTIy9Y2j7Bb9bzq8Vmvz+h7d18Uig9+/IBntz1Lek661tFuSS7iV9Hry8nlrbV7+PxE0Q3vwurVYH7fpgRUd6/wdQsh7FxBLrwXCr+dgQ4vQfc3tE4k7NzXKV8zdc9Urhdep5qxGjM7zaRjrY5WWXdZtt9S3JQTk1nxfUoGF7Nz8fVwpt1d1TmUdJY1a+Mwmm6wKb8xT3drwehuDXHQ68ptvUKIKi7pa/j8CdA7wYg9cC296MfdD+q0LzoAWQtmE5zdUzmyiHJ1Nuss474dx4mMEwA80/QZXmz1Ik7oMZ1J5GDaD1xy0FMzqD0h/m1xKKffu80VN4sXL2bu3LmkpaXRokULFi5cSLt27W7ZPi4ujilTpnDmzBkaNmzI7Nmz6dWrV6nWVRHFzZZjqbzx1XFSM3P/f46iuctVWqgUHHVmcnGiY4+HeDCsebmsTwghLJSCVX3h1A5wNEJh3h/PeQZCz9nQpLd1Mx3fAFtehawL2mcRFSLPlMe8H+YRmxQLQEv3YHqnpfC+syLd8Y/DLfycPJnQ4Q3C64Tf8Tpt6vYLq1evJioqiujoaA4ePEiLFi2IiIjg4sWLJbbfs2cPTz75JMOGDePQoUP06dOHPn36cOzYMSsnL7LlWCojVh60FDaOmOjkdIbWnMZRZ+aqYzVeeP4FKWyEEBVDp4OGPYqm/1zYAGSlwpqni4oNazm+oWidfy5stMoiKozRwcikf01ifpf5eDg4c/jaOaa56Ul3KD5KczE/k6iEl9lxdodV82k+chMaGkrbtm1ZtGgRAGazmaCgIEaPHs2ECRNuat+/f39ycnLYuHGjZd6//vUvWrZsydKlS/9xfeU5cmMyKzrO/sZS2FTTXaeLIRlvfS5mBYcKA7nkeheJE+6XXVFCiIphNsGCpjcXExa6ot1Cz2yp+N1CZhOsiCjaFaV1FmEdZhNnYiJ4pLqBQl3J2zmdUvi5+bPlsa13tIuqLNtvTU/Vyc/P58CBA0ycONEyT6/XEx4ezt69JZ9Lv3fvXqKioorNi4iIYP369SW2z8vLIy/vj28zWVlZdx78/32fkvGnXVEQ7HAVb30uOcqJb/PrkW72gKw8vk/JIKx+jXJbrxBCWJzd8zeFDYCCa2nwbktrJfoblSmLKC+XnI0U6vxu+bzS6Ui7ns7Biwdp62+da7ppWtxcvnwZk8mEn1/xTvHz8+PkyZMlLpOWllZi+7S0tBLbv/XWW7zxRsWcQXAxO7fY4x8LA9CjOF7oSx5Ot2wnhBDl5pajJH+hd7LOyI25oHJkEdZhNnHJoXS/y0vXL1VwmD/Y/UVWJk6cWGykJysri6CgoHJ5bV8P52KPFToOFdb6x3ZCCFFu3G/9jbmYQevgrk4VmyVlF3z8UOXIIqwjZRc1Vz9WqqY1XWtWcJg/aHpAsY+PDw4ODqSnF//mkZ6ejr+/f4nL+Pv7l6m90WjE09Oz2E95aXdXdQK8nLnV0TQ6IMCr6LRwIYSoEHXaF52J9HefRJ61itpVpSzCOuq0J8RQA7/CQnS3OIRXpxT+rn6E+IZYLZamxY3BYKB169bEx8db5pnNZuLj4wkLCytxmbCwsGLtAbZv337L9hXJQa8jOrIJcPO/8u+PoyObyMHEQoiKo3coOsUauOUnUc9Z1tkNVJmyCOvQO+DQczYTrlwFuKnA0SkFOh2vtptQbte7KVUsq63pFqKioli2bBkff/wxJ06cYMSIEeTk5DB06FAAnn766WIHHI8dO5YtW7bw9ttvc/LkSaZOncr+/ft58cUXNcnfs2kAS54Kwd+r+K4nfy9nljwVQs+mAZrkEkJUIU16Q79PwPMvnzeegUXzrXltmcqURVhHk96EP/Q+86+Z8TWZij3lZ/Rmfpd3yuU6N2Wh+angAIsWLbJcxK9ly5a8++67hIaGAtClSxfq1q1LTEyMpX1cXByTJ0+2XMRvzpw5ml7ED0q+QrGM2AghrKoyXRW4MmUR1mE2yRWKtWLte0sJIYQQ4s7Z1BWKhRBCCCHKkxQ3QgghhLArUtwIIYQQwq5IcSOEEEIIuyLFjRBCCCHsihQ3QgghhLArUtwIIYQQwq5IcSOEEEIIuyLFjRBCCCHsiqPWAazt9wsyZ2VlaZxECCGEEKX1+3a7NDdWqHLFTXZ2NgBBQUEaJxFCCCFEWWVnZ+Pl5fW3barcvaXMZjMXLlzAw8MDna58b2yZlZVFUFAQ58+fl/tWVSDpZ+uQfrYO6Wfrkb62jorqZ6UU2dnZBAYGotf//VE1VW7kRq/XU7t27Qpdh6enp/zjWIH0s3VIP1uH9LP1SF9bR0X08z+N2PxODigWQgghhF2R4kYIIYQQdkWKm3JkNBqJjo7GaDRqHcWuST9bh/SzdUg/W4/0tXVUhn6ucgcUCyGEEMK+yciNEEIIIeyKFDdCCCGEsCtS3AghhBDCrkhxI4QQQgi7IsVNGS1evJi6devi7OxMaGgo33///d+2j4uLo1GjRjg7O9OsWTM2b95spaS2rSz9vGzZMjp16kS1atWoVq0a4eHh//h7EUXK+vf8u9jYWHQ6HX369KnYgHairP189epVRo0aRUBAAEajkbvvvls+O0qhrP28YMEC7rnnHlxcXAgKCuLll18mNzfXSmlt03fffUdkZCSBgYHodDrWr1//j8skJCQQEhKC0WikQYMGxMTEVHhOlCi12NhYZTAY1IoVK9RPP/2khg8frry9vVV6enqJ7Xfv3q0cHBzUnDlz1PHjx9XkyZOVk5OTOnr0qJWT25ay9vOAAQPU4sWL1aFDh9SJEyfUkCFDlJeXl/r111+tnNy2lLWff5eSkqJq1aqlOnXqpB5++GHrhLVhZe3nvLw81aZNG9WrVy+VmJioUlJSVEJCgjp8+LCVk9uWsvbzqlWrlNFoVKtWrVIpKSlq69atKiAgQL388stWTm5bNm/erCZNmqS+/PJLBah169b9bfvk5GTl6uqqoqKi1PHjx9XChQuVg4OD2rJlS4XmlOKmDNq1a6dGjRpleWwymVRgYKB66623Smzfr18/9eCDDxabFxoaqp5//vkKzWnrytrPf1VYWKg8PDzUxx9/XFER7cLt9HNhYaFq3769Wr58uRo8eLAUN6VQ1n5esmSJqlevnsrPz7dWRLtQ1n4eNWqU6tatW7F5UVFRqkOHDhWa056UprgZP368uvfee4vN69+/v4qIiKjAZErJbqlSys/P58CBA4SHh1vm6fV6wsPD2bt3b4nL7N27t1h7gIiIiFu2F7fXz391/fp1CgoKqF69ekXFtHm328/Tpk3D19eXYcOGWSOmzbudft6wYQNhYWGMGjUKPz8/mjZtysyZMzGZTNaKbXNup5/bt2/PgQMHLLuukpOT2bx5M7169bJK5qpCq+1glbtx5u26fPkyJpMJPz+/YvP9/Pw4efJkicukpaWV2D4tLa3Cctq62+nnv3r11VcJDAy86R9K/OF2+jkxMZEPP/yQw4cPWyGhfbidfk5OTuabb75h4MCBbN68mVOnTjFy5EgKCgqIjo62Rmybczv9PGDAAC5fvkzHjh1RSlFYWMgLL7zAa6+9Zo3IVcattoNZWVncuHEDFxeXClmvjNwIuzJr1ixiY2NZt24dzs7OWsexG9nZ2QwaNIhly5bh4+OjdRy7Zjab8fX15YMPPqB169b079+fSZMmsXTpUq2j2ZWEhARmzpzJe++9x8GDB/nyyy/ZtGkT06dP1zqaKAcyclNKPj4+ODg4kJ6eXmx+eno6/v7+JS7j7+9fpvbi9vr5d/PmzWPWrFns2LGD5s2bV2RMm1fWfj59+jRnzpwhMjLSMs9sNgPg6OhIUlIS9evXr9jQNuh2/p4DAgJwcnLCwcHBMq9x48akpaWRn5+PwWCo0My26Hb6ecqUKQwaNIhnn30WgGbNmpGTk8Nzzz3HpEmT0Ovlu395uNV20NPTs8JGbUBGbkrNYDDQunVr4uPjLfPMZjPx8fGEhYWVuExYWFix9gDbt2+/ZXtxe/0MMGfOHKZPn86WLVto06aNNaLatLL2c6NGjTh69CiHDx+2/PTu3ZuuXbty+PBhgoKCrBnfZtzO33OHDh04deqUpXgE+PnnnwkICJDC5hZup5+vX79+UwHze0Gp5JaL5Uaz7WCFHq5sZ2JjY5XRaFQxMTHq+PHj6rnnnlPe3t4qLS1NKaXUoEGD1IQJEyztd+/erRwdHdW8efPUiRMnVHR0tJwKXgpl7edZs2Ypg8Gg1q5dq1JTUy0/2dnZWr0Fm1DWfv4rOVuqdMraz+fOnVMeHh7qxRdfVElJSWrjxo3K19dXvfnmm1q9BZtQ1n6Ojo5WHh4e6vPPP1fJyclq27Ztqn79+qpfv35avQWbkJ2drQ4dOqQOHTqkADV//nx16NAhdfbsWaWUUhMmTFCDBg2ytP/9VPBx48apEydOqMWLF8up4JXRwoULVXBwsDIYDKpdu3bqP//5j+W5zp07q8GDBxdrv2bNGnX33Xcrg8Gg7r33XrVp0yYrJ7ZNZennOnXqKOCmn+joaOsHtzFl/Xv+MyluSq+s/bxnzx4VGhqqjEajqlevnpoxY4YqLCy0cmrbU5Z+LigoUFOnTlX169dXzs7OKigoSI0cOVL99ttv1g9uQ3bu3Fni5+3vfTt48GDVuXPnm5Zp2bKlMhgMql69euqjjz6q8Jw6pWT8TQghhBD2Q465EUIIIYRdkeJGCCGEEHZFihshhBBC2BUpboQQQghhV6S4EUIIIYRdkeJGCCGEEHZFihshhBBC2BUpboQQQghhV6S4EUJoYsiQIfTp0+eOXyc+Pp7GjRtjMpnuPFQ52r17N82aNcPJyanU7/OJJ57g7bffrthgQlQBUtwIISyGDBmCTqdDp9NhMBho0KAB06ZNo7Cw0OpZEhISLFl0Oh1+fn489thjJCcnF2s3fvx4Jk+eXOwu2pVBVFQULVu2JCUlhZiYmFItM3nyZGbMmEFmZmbFhhPCzklxI4QopmfPnqSmpvLLL7/wyiuvMHXqVObOnVti2/z8/ArPk5SUxIULF4iLi+Onn34iMjLSMkqTmJjI6dOneeyxxyo8R1mdPn2abt26Ubt2bby9vUu1TNOmTalfvz4rV66s2HBC2DkpboQQxRiNRvz9/alTpw4jRowgPDycDRs2AH/sSpoxYwaBgYHcc889AJw/f55+/frh7e1N9erVefjhhzlz5ozlNU0mE1FRUXh7e1OjRg3Gjx9PaW9r5+vrS0BAAPfddx+vv/46x48f59SpUwDExsbSvXt3nJ2dLe1L2t310ksv0aVLF8vjLl26MGbMGMaPH0/16tXx9/dn6tSplueVUkydOpXg4GCMRiOBgYGMGTPG8vynn35KmzZt8PDwwN/fnwEDBnDx4kUAzpw5g06n48qVKzzzzDPodDrLyM2xY8d44IEHcHd3x8/Pj0GDBnH58uViWSMjI4mNjS1V3wghSibFjRDib7m4uBQboYmPjycpKYnt27ezceNGCgoKiIiIwMPDg127drF7927c3d3p2bOnZbm3336bmJgYVqxYQWJiIhkZGaxbt+62ssAfI0a7du2iTZs2t/W+Pv74Y9zc3Ni3bx9z5sxh2rRpbN++HYAvvviCd955h/fff59ffvmF9evX06xZM8uyBQUFTJ8+nSNHjrB+/XrOnDnDkCFDAAgKCiI1NRVPT08WLFhAamoq/fv35+rVq3Tr1o1WrVqxf/9+tmzZQnp6Ov369SuWq127dnz//ffk5eXd1vsSQoCj1gGEEJWTUor4+Hi2bt3K6NGjLfPd3NxYvnw5BoMBgJUrV2I2m1m+fDk6nQ6Ajz76CG9vbxISEujRowcLFixg4sSJPProowAsXbqUrVu3lilPamoq8+bNo1atWpYRo7NnzxIYGHhb76958+ZER0cD0LBhQxYtWkR8fDzdu3fn3Llz+Pv7Ex4ejpOTE8HBwbRr186y7DPPPGOZrlevHu+++y5t27bl2rVruLu74+/vj06nw8vLC39/f6CowGvVqhUzZ860LLtixQqCgoL4+eefufvuuwEIDAwkPz+ftLQ06tSpc1vvTYiqTkZuhBDFbNy4EXd3d5ydnXnggQfo379/sV02zZo1sxQ2AEeOHOHUqVN4eHjg7u6Ou7s71atXJzc3l9OnT5OZmUlqaiqhoaGWZRwdHUs94lK7dm3c3NwIDAwkJyeHL774wrL+GzduFNslVRbNmzcv9jggIMCya+nxxx/nxo0b1KtXj+HDh7Nu3bpiB1UfOHCAyMhIgoOD8fDwoHPnzgCcO3fulus7cuQIO3futPSRu7s7jRo1AoqOz/nd76NT169fv633JYSQkRshxF907dqVJUuWYDAYCAwMxNGx+MeEm5tbscfXrl2jdevWrFq16qbXqlmz5h3n2bVrF56envj6+uLh4VHsOR8fH3777bdi8/R6/U3H8xQUFNz0uk5OTsUe63Q6zGYzULRrKSkpiR07drB9+3ZGjhzJ3Llz+fbbb8nPzyciIoKIiAhWrVpFzZo1OXfuHBEREX97gPW1a9eIjIxk9uzZNz0XEBBgmc7IyADKp++EqKqkuBFCFOPm5kaDBg1K3T4kJITVq1fj6+uLp6dniW0CAgLYt28f9913HwCFhYUcOHCAkJCQf3z9u+6665ZnG7Vq1Yrjx48Xm1ezZk2OHTtWbN7hw4dvKmb+iYuLC5GRkURGRjJq1CgaNWrE0aNHUUpx5coVZs2aRVBQEAD79+//x9cLCQnhiy++oG7dujcVjH927NgxateujY+PT5nyCiH+ILulhBB3ZODAgfj4+PDwww+za9cuUlJSSEhIYMyYMfz6668AjB07llmzZrF+/XpOnjzJyJEjuXr16h2vOyIigsTExGLzunXrxv79+/nkk0/45ZdfiI6OvqnY+ScxMTF8+OGHHDt2jOTkZFauXImLiwt16tQhODgYg8HAwoULSU5OZsOGDUyfPv0fX3PUqFFkZGTw5JNP8sMPP3D69Gm2bt3K0KFDi12AcNeuXfTo0aNMeYUQxUlxI4S4I66urnz33XcEBwfz6KOP0rhxY4YNG0Zubq5lJOeVV15h0KBBDB48mLCwMDw8PHjkkUfueN0DBw7kp59+IikpyTIvIiKCKVOmMH78eNq2bUt2djZPP/10mV7X29ubZcuW0aFDB5o3b86OHTv46quvqFGjBjVr1iQmJoa4uDiaNGnCrFmzmDdv3j++ZmBgILt378ZkMtGjRw+aNWvGSy+9hLe3N3p90Udxbm4u69evZ/jw4WXrCCFEMTpV2otNCCFEJTRu3DiysrJ4//33tY5yx5YsWcK6devYtm2b1lGEsGkyciOEsGmTJk2iTp06loOBbZmTkxMLFy7UOoYQNk9GboQQQghhV2TkRgghhBB2RYobIYQQQtgVKW6EEEIIYVekuBFCCCGEXZHiRgghhBB2RYobIYQQQtgVKW6EEEIIYVekuBFCCCGEXZHiRgghhBB25f8AKgcPqXrUb/QAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from sklearn.calibration import calibration_curve\n", "\n", "# Reliability\n", "for name, proba in [(\"LGB\", y_proba_lgb), (\"NGBoost\", p_unsafe_ngb), (\"QRF\", p_unsafe_qrf)]:\n", "    prob_true, prob_pred = calibration_curve(y_test, proba, n_bins=10)\n", "    plt.plot(prob_pred, prob_true, marker='o', label=name)\n", "plt.plot([0,1],[0,1],\"--\", color=\"gray\")\n", "plt.legend(), plt.title(\"Reliability\"), plt.xlabel(\"Pred P(unsafe)\"), plt.ylabel(\"Obs P(unsafe)\")\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}